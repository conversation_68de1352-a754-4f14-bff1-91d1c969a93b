kind: Deployment
apiVersion: apps/v1
metadata:
  name: af-safecheck
  namespace: v4service-release
  labels:
    app: af-safecheck
  annotations:
    kubesphere.io/creator: aofeng
    kubesphere.io/description: V4安检服务
spec:
  replicas: 1
  selector:
    matchLabels:
      app: af-safecheck
  template:
    metadata:
      creationTimestamp: null
      labels:
        app: af-safecheck
      annotations:
        logging.kubesphere.io/logsidecar-config: '{}'
    spec:
      volumes:
        - name: host-time
          hostPath:
            path: /etc/localtime
            type: ''
        - name: java-pvc
          hostPath:
            path: /var/af/v4env-release/java/java.security
            type: ''
        - name: app-logs
          hostPath:
            path: /var/af/v4service-release
            type: ''
        - name: client-dist
          hostPath:
            path: /var/af/standard-release/client/dist
            type: ''
        - name: files
          hostPath:
            path: /var/af/v4service-release/v4files
            type: ''
      containers:
        - name: af-safecheck
          image: ''
          ports:
            - name: http-9001
              containerPort: 9001
              protocol: TCP
          env:
            - name: spring.cloud.nacos.config.server-addr
              valueFrom:
                configMapKeyRef:
                  name: nacos-register
                  key: spring.cloud.nacos.config.server-addr
            - name: spring.cloud.nacos.discovery.server-addr
              valueFrom:
                configMapKeyRef:
                  name: nacos-register
                  key: spring.cloud.nacos.discovery.server-addr
            - name: spring.profiles.active
              valueFrom:
                configMapKeyRef:
                  name: nacos-register
                  key: spring.profiles.active
            - name: TZ
              valueFrom:
                configMapKeyRef:
                  name: tz-conf
                  key: TZ
            - name: NODE_NAME
              valueFrom:
                fieldRef:
                  apiVersion: v1
                  fieldPath: spec.nodeName
          resources: {}
          volumeMounts:
            - name: host-time
              readOnly: true
              mountPath: /etc/localtime
            - name: java-pvc
              readOnly: true
              mountPath: /usr/lib/jvm/liberica21-container-jre/conf/security/java.security
            - name: app-logs
              mountPath: '/application/afServer/afLogs'
            - name: files
              mountPath: /usr/local/tomcat/files
            - name: client-dist
              mountPath: /usr/local/dist
          terminationMessagePath: /dev/termination-log
          terminationMessagePolicy: File
          imagePullPolicy: IfNotPresent
      restartPolicy: Always
      terminationGracePeriodSeconds: 30
      dnsPolicy: ClusterFirst
      serviceAccountName: default
      serviceAccount: default
      securityContext: {}
      imagePullSecrets:
        - name: harbor-inner-server
      affinity: {}
      schedulerName: default-scheduler
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxUnavailable: 25%
      maxSurge: 25%
  revisionHistoryLimit: 10
  progressDeadlineSeconds: 600
