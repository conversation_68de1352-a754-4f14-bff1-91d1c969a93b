package com.af.v4.safecheck.v3.timer;

import com.af.v4.system.common.logic.service.LogicService;
import org.json.JSONArray;
import org.json.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @Date 2022-01-14
 * <p>
 * 概要：温宿定时拉黑接口
 */
@Component
public class WenSuBatchMask {
    private final static Logger log = LoggerFactory.getLogger(WenSuBatchMask.class);
    private final LogicService logicService;

    public WenSuBatchMask(LogicService logicService) {
        this.logicService = logicService;
    }

    public void batchMask() {
        try {
            JSONArray query = new JSONArray(logicService.run("safe_get_blactlist", "{data:{}}"));
            if (!query.isEmpty()) {
                log.info("获取用户信息---{}", query);
                JSONObject obj = new JSONObject();
                obj.put("condition", " 1=1");
                obj.put("switchCheckAll", false);
                obj.put("checkAll", false);
                obj.put("param", query);
                obj.put("type", "send");
                obj.put("columnName", "f_userinfo_code");
                log.info("obj========---{}", obj);
                logicService.run("safe_getuser_to_blactlist", "{data:" + obj + "}");
                log.info("执行完成");
            }
        } catch (Exception e) {
            log.error("安检系统向第三方推送拉黑信息，执行错误，错误原因：\n", e);
        }
    }
}
