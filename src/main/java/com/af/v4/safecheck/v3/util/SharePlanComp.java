package com.af.v4.safecheck.v3.util;

import org.json.JSONArray;
import org.json.JSONObject;

/**
 * com.aote.util
 *
 * <AUTHOR>
 * @creater 2021-05-31 17:17
 */
public class SharePlanComp {

    public JSONObject compAndroidPc(JSONArray androids, JSONArray pcs) {
        JSONArray delete;
        JSONArray insert;
        JSONArray update = new JSONArray();
        JSONObject changed = new JSONObject();
        for (int i = 1; i <= androids.length(); i++) {

            for (int j = 1; j <= pcs.length(); j++) {
                JSONObject android = androids.getJSONObject(i - 1);
                JSONObject pc = pcs.getJSONObject(j - 1);
                if (pc.getString("id").equals(android.getString("id"))) {
                    androids.remove(i - 1);
                    pcs.remove(j - 1);
                    //i=i==0?0:i-1;
                    i = i - 1;

                    if (pc.getInt("version") > android.getInt("version") ||
                            (pc.getString("f_state").equals("已检") && android.getString("f_state").equals("未检"))
                    ) {
                        update.put(pc);
                    }
                    break;
                }

            }
        }
        delete = androids;
        insert = pcs;
        changed.put("insert", insert);
        changed.put("update", update);
        changed.put("delete", delete);
        return changed;
    }

}

