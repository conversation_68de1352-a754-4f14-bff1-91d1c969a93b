package com.af.v4.safecheck.v3.timer;

import com.af.v4.system.common.logic.service.LogicService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @Date 2022-01-14
 * <p>
 * 概要：置换定时器建档
 */
@Component
public class ShieldingTimer {
    private final static Logger log = LoggerFactory.getLogger(ShieldingTimer.class);
    private final LogicService logicService;

    public ShieldingTimer(LogicService logicService) {
        this.logicService = logicService;
    }

    public void sendWorkTimer() {
        try {
            logicService.run("EntryStatus", "{data:{}}");
            logicService.run("EntryStatusNoMeet", "{data:{}}");
            log.info("执行安检系统定时器拉黑逻辑结束");
        } catch (Exception e) {
            log.error("安检系统定时器拉黑，执行错误，错误原因：\n", e);
        }
    }
}
