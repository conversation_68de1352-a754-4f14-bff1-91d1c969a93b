package com.af.v4.safecheck.v3.timer;

import com.af.v4.system.common.logic.service.LogicService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

/**
 * @Author: 作者名
 * @Date: 2024/4/10
 */
/*
 * 安检册自动下发
 * 定时任务
 * */
@Component
public class AutomaticDeliveryTimer {
    private static final Logger log = LoggerFactory.getLogger(AutomaticDeliveryTimer.class);
    private final LogicService logicService;
    private String logicName;
    private String logicPath;

    public AutomaticDeliveryTimer(LogicService logicService) {
        this.logicService = logicService;
    }

    public String getLogicName() {
        return logicName;
    }

    public void setLogicName(String logicName) {
        this.logicName = logicName;
    }

    public String getLogicPath() {
        return logicPath;
    }

    public void setLogicPath(String logicPath) {
        this.logicPath = logicPath;
    }

    public void automaticDeliveryPlan() {
        deliveryPlan();
        log.info("安检册定时下发，定时器执行了");
    }

    public void deliveryPlan() {
        logicService.run("AutomaticDelivery", "{data:{}}");
    }
}
