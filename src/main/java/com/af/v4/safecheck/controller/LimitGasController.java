package com.af.v4.safecheck.controller;

import com.af.v4.safecheck.entity.LimitGas;
import com.af.v4.system.common.jpa.service.SqlService;
import org.json.JSONArray;
import org.json.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.*;
import org.springframework.transaction.annotation.Transactional;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.HashMap;
import java.util.Map;
import java.util.UUID;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import com.af.v4.safecheck.dto.RelieveLimitTestRequest;
import com.af.v4.safecheck.dto.AddLimitGasTestRequest;

/**
 * 限气表控制器
 * 用于查询返回字段，条件：f_State = '有效'
 */
@RestController
@RequestMapping("/limitgas")
public class LimitGasController {

    private static final Logger LOG = LoggerFactory.getLogger(LimitGasController.class);
    private final SqlService sqlService;

    public LimitGasController(SqlService sqlService) {
        this.sqlService = sqlService;
    }

    /**
     * 查询所有有效的限气记录
     * @return 返回有效状态的限气记录列表
     */
    @GetMapping("/valid")
    @ResponseBody
    public Map<String, Object> getValidLimitGas() {
        Map<String, Object> result = new HashMap<>();
        try {
            // 构建查询SQL（尝试不同的字段名）
            String sql = "SELECT * FROM t_limit_gas WHERE f_State = '有效' ORDER BY f_operate_date DESC";

            // 执行查询
            JSONArray queryResult = sqlService.querySQL("查询有效限气记录", sql);

            result.put("code", 200);
            result.put("message", "查询成功");
        } catch (Exception e) {
            result.put("code", 500);
            result.put("message", "查询失败：" + e.getMessage());
            result.put("data", new ArrayList<>());
            result.put("total", 0);
        }

        return result;
    }

    /**
     * 解除限购接口
     * @param request 解除限购请求参数
     * @return 返回解除限购结果
     */
    @PostMapping("/cc1")
    @ResponseBody
    @Transactional(rollbackFor = Exception.class)
    public Map<String, Object> relieveLimit(@RequestBody RelieveLimitTestRequest request) {
        Map<String, Object> result = new HashMap<>();

        try {

            // 1. 根据限购ID修改t_limit_Gas表的f_State='无效'
            String updateSql = "UPDATE t_limit_gas SET f_State = '无效' WHERE id = " + request.getFLimitId();
            sqlService.execSQL("更新限购记录状态", updateSql);

            // 2. 查询用户信息用于构建描述
            String queryUserSql = "SELECT lg.f_userinfo_id, ui.f_user_name, uf.f_userfiles_id " +
                                 "FROM t_limit_gas lg " +
                                 "LEFT JOIN t_userinfo ui ON lg.f_userinfo_id = ui.f_userinfo_id " +
                                 "LEFT JOIN t_userfiles uf ON lg.f_userinfo_id = uf.f_userinfo_id " +
                                 "WHERE lg.id = " + request.getFLimitId();
            JSONArray userResult = sqlService.querySQL("查询用户信息", queryUserSql);

            String userName = "";
            String userfilesId = "";
            String userinfoId = "";
            if (userResult.length() > 0) {
                JSONObject userInfo = userResult.getJSONObject(0);
                userName = userInfo.optString("f_user_name", "");
                userfilesId = userInfo.optString("f_userfiles_id", "");
                userinfoId = userInfo.optString("f_userinfo_id", "");
            }

            // 3. 在t_record表新增一条"解除限购"记录
            String operateType = "解除限购";
            String description = (request.getOperator() != null ? request.getOperator() : "") +
                               "对客户" + userName + "进行" + operateType + "操作";

            String insertRecordSql = "INSERT INTO t_record (" +
                    "f_operat_type, f_describe, f_state, f_comments, f_operate_date, " +
                    "f_userfiles_id, f_userinfo_id, f_user_id, f_orgid, f_orgname, " +
                    "f_depid, f_depname, f_operatorid, f_operator, f_user_name" +
                    ") VALUES (" +
                    "'" + operateType + "', " +
                    "'" + description + "', " +
                    "'有效', " +
                    "'" + (request.getFVoidRemarks() != null ? request.getFVoidRemarks() : "") + "', " +
                    "'" + (request.getOperateDate() != null ? request.getOperateDate() : LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"))) + "', " +
                    (userfilesId.isEmpty() ? "NULL" : userfilesId) + ", " +
                    (userinfoId.isEmpty() ? "NULL" : userinfoId) + ", " +
                    "'" + userinfoId + "', " +
                    "'" + (request.getOrgId() != null ? request.getOrgId() : "") + "', " +
                    "'" + (request.getOrgName() != null ? request.getOrgName() : "") + "', " +
                    "'" + (request.getDepId() != null ? request.getDepId() : "") + "', " +
                    "'" + (request.getDepName() != null ? request.getDepName() : "") + "', " +
                    "'" + (request.getOperatorId() != null ? request.getOperatorId() : "") + "', " +
                    "'" + (request.getOperator() != null ? request.getOperator() : "") + "', " +
                    "'" + userName + "'" +
                    ")";

            sqlService.execSQL("插入操作记录", insertRecordSql);

            // 4. 获取刚插入的记录ID，更新t_limit_gas的f_cancle_record_id
            String getRecordIdSql = "SELECT TOP 1 id FROM t_record WHERE f_operat_type = '" + operateType +
                                   "' AND f_user_id = '" + userinfoId + "' ORDER BY id DESC";
            JSONArray recordResult = sqlService.querySQL("获取记录ID", getRecordIdSql);

            if (recordResult.length() > 0) {
                String recordId = recordResult.getJSONObject(0).optString("id");
                String updateRecordIdSql = "UPDATE t_limit_gas SET f_cancle_record_id = " + recordId +
                                          " WHERE id = " + request.getFLimitId();
                sqlService.execSQL("更新取消记录ID", updateRecordIdSql);
            }

            // 5. 构建返回数据
            Map<String, Object> data = new HashMap<>();
            data.put("limitId", request.getFLimitId());
            data.put("status", "已解除");

            result.put("code", 200);
            result.put("message", "解除限购成功");
            result.put("data", data);
            result.put("success", true);

        } catch (Exception e) {
            result.put("code", 500);
            result.put("message", "解除限购失败：" + e.getMessage());
            result.put("success", false);
            // @Transactional注解会自动回滚事务
            throw e; // 重新抛出异常以触发事务回滚
        }

        return result;
    }

    /**
     * 新增限购接口
     * @param request 新增限购请求参数
     * @return 返回新增限购结果
     */
    @PostMapping("/vv1")
    @ResponseBody
    @Transactional(rollbackFor = Exception.class)
    public Map<String, Object> addLimitGas(@RequestBody AddLimitGasTestRequest request) {
        Map<String, Object> result = new HashMap<>();

        // 验证必要参数
        if (request.getFUserinfoId() == null || request.getFUserinfoId().trim().isEmpty()) {
            result.put("success", false);
            result.put("message", "用户信息ID不能为空");
            result.put("code", 400);
            return result;
        }

        try {

            // 1. 根据f_userinfo_id查询t_limit_Gas表，判断是新增还是修改
            String queryExistingSql = "SELECT * FROM t_limit_gas WHERE f_userinfo_id = " + request.getFUserinfoId() + " AND f_State = '有效'";
            JSONArray existingRecords = sqlService.querySQL("查询现有限气记录", queryExistingSql);

            boolean isUpdate = existingRecords.length() > 0;
            String operationType = isUpdate ? "修改限购" : "新增限购";

            // 获取用户信息
            String getUserInfoSql = "SELECT ui.f_user_name, uf.f_userfiles_id " +
                                   "FROM t_userinfo ui " +
                                   "LEFT JOIN t_userfiles uf ON ui.f_userinfo_id = uf.f_userinfo_id " +
                                   "WHERE ui.f_userinfo_id = " + request.getFUserinfoId();
            JSONArray userInfoResult = sqlService.querySQL("获取用户信息", getUserInfoSql);

            String userName = "";
            String userfilesId = "";
            if (userInfoResult.length() > 0) {
                JSONObject userInfo = userInfoResult.getJSONObject(0);
                userName = userInfo.optString("f_user_name", "");
                userfilesId = userInfo.optString("f_userfiles_id", "");
            }

            // 2. 执行新增或修改操作
            String sql;
            Integer limitRecordId = null;
            if (isUpdate) {
                // 修改现有记录
                JSONObject existingRecord = existingRecords.getJSONObject(0);
                limitRecordId = existingRecord.getInt("id");

                sql = "UPDATE t_limit_gas SET " +
                      "f_limit_style = '" + (request.getFLimitStyle() != null ? request.getFLimitStyle() : "") + "', " +
                      "f_time_type = '" + (request.getFTimeType() != null ? request.getFTimeType() : "") + "', " +
                      "f_limit_type = '" + (request.getFLimitType() != null ? request.getFLimitType() : "") + "', " +
                      "f_start_date = '" + (request.getFStartDate() != null ? request.getFStartDate() : "") + "', " +
                      "f_time_value = '" + (request.getFTimeValue() != null ? request.getFTimeValue() : "") + "', " +
                      "f_third_pay = '" + (request.getFThirdPay() != null && !request.getFThirdPay().trim().isEmpty() ? request.getFThirdPay() : "0") + "', " +
                      "f_limit_times = '" + (request.getFLimitTimes() != null ? request.getFLimitTimes() : "") + "', " +
                      "f_operate_date = '" + (request.getOperateDate() != null ? request.getOperateDate() : LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"))) + "', " +
                      "f_operate_people = '" + (request.getOperator() != null ? request.getOperator() : "") + "', " +
                      "f_operate_peopleid = '" + (request.getOperatorId() != null ? request.getOperatorId() : "") + "' " +
                      "WHERE id = " + limitRecordId;
            } else {
                // 新增记录
                sql = "INSERT INTO t_limit_gas (" +
                      "f_userinfo_id, f_limit_style, f_time_type, f_limit_type, f_start_date, " +
                      "f_time_value, f_third_pay, f_limit_times, f_operate_date, f_operate_people, " +
                      "f_operate_peopleid, f_state" +
                      ") VALUES (" +
                      request.getFUserinfoId() + ", " +
                      "'" + (request.getFLimitStyle() != null ? request.getFLimitStyle() : "") + "', " +
                      "'" + (request.getFTimeType() != null ? request.getFTimeType() : "") + "', " +
                      "'" + (request.getFLimitType() != null ? request.getFLimitType() : "") + "', " +
                      "'" + (request.getFStartDate() != null ? request.getFStartDate() : "") + "', " +
                      "'" + (request.getFTimeValue() != null ? request.getFTimeValue() : "") + "', " +
                      (request.getFThirdPay() != null && !request.getFThirdPay().trim().isEmpty() ? request.getFThirdPay() : "0") + ", " +
                      "'" + (request.getFLimitTimes() != null ? request.getFLimitTimes() : "") + "', " +
                      "'" + (request.getOperateDate() != null ? request.getOperateDate() : LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"))) + "', " +
                      "'" + (request.getOperator() != null ? request.getOperator() : "") + "', " +
                      "'" + (request.getOperatorId() != null ? request.getOperatorId() : "") + "', " +
                      "'有效'" +
                      ")";
            }

            sqlService.execSQL(operationType, sql);

            // 3. 在t_record表新增操作记录
            String description = (request.getOperator() != null ? request.getOperator() : "") +
                               "对客户" + userName + "进行" + operationType + "操作";

            String insertRecordSql = "INSERT INTO t_record (" +
                    "f_operat_type, f_describe, f_state, f_comments, f_operate_date, " +
                    "f_userfiles_id, f_userinfo_id, f_user_id, f_orgid, f_orgname, " +
                    "f_depid, f_depname, f_operatorid, f_operator, f_user_name" +
                    ") VALUES (" +
                    "'" + operationType + "', " +
                    "'" + description + "', " +
                    "'有效', " +
                    "'', " +
                    "'" + (request.getOperateDate() != null ? request.getOperateDate() : LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"))) + "', " +
                    (userfilesId.isEmpty() ? "NULL" : userfilesId) + ", " +
                    request.getFUserinfoId() + ", " +
                    "'" + request.getFUserinfoId() + "', " +
                    "'" + (request.getOrgId() != null ? request.getOrgId() : "") + "', " +
                    "'" + (request.getOrgName() != null ? request.getOrgName() : "") + "', " +
                    "'" + (request.getDepId() != null ? request.getDepId() : "") + "', " +
                    "'" + (request.getDepName() != null ? request.getDepName() : "") + "', " +
                    "'" + (request.getOperatorId() != null ? request.getOperatorId() : "") + "', " +
                    "'" + (request.getOperator() != null ? request.getOperator() : "") + "', " +
                    "'" + userName + "'" +
                    ")";

            sqlService.execSQL("插入操作记录", insertRecordSql);

            // 4. 获取刚插入的记录ID，更新t_limit_gas的f_limit_recod_id
            String getRecordIdSql = "SELECT TOP 1 id FROM t_record WHERE f_operat_type = '" + operationType +
                                   "' AND f_user_id = '" + request.getFUserinfoId() + "' ORDER BY id DESC";
            JSONArray recordResult = sqlService.querySQL("获取记录ID", getRecordIdSql);

            if (recordResult.length() > 0) {
                String recordIdStr = recordResult.getJSONObject(0).optString("id");
                String updateRecordIdSql;

                if (isUpdate && limitRecordId != null) {
                    // 修改操作：更新现有记录的f_limit_recod_id
                    updateRecordIdSql = "UPDATE t_limit_gas SET f_limit_recod_id = " + recordIdStr +
                                       " WHERE id = " + limitRecordId;
                } else {
                    // 新增操作：获取刚插入的限购记录ID并更新f_limit_recod_id
                    String getLimitIdSql = "SELECT TOP 1 id FROM t_limit_gas WHERE f_userinfo_id = " +
                                          request.getFUserinfoId() + " AND f_state = '有效' ORDER BY id DESC";
                    JSONArray limitResult = sqlService.querySQL("获取限购记录ID", getLimitIdSql);
                    if (limitResult.length() > 0) {
                        String limitIdStr = limitResult.getJSONObject(0).optString("id");
                        updateRecordIdSql = "UPDATE t_limit_gas SET f_limit_recod_id = " + recordIdStr +
                                           " WHERE id = " + limitIdStr;
                    } else {
                        updateRecordIdSql = null;
                    }
                }

                if (updateRecordIdSql != null) {
                    sqlService.execSQL("更新限购记录关联ID", updateRecordIdSql);
                }
            }

            // 5. 构建返回数据
            Map<String, Object> data = new HashMap<>();
            data.put("userinfo_id", request.getFUserinfoId());
            data.put("operation_type", operationType);

            result.put("success", true);
            result.put("message", operationType + "成功");
            result.put("data", data);
            result.put("code", 200);

        } catch (Exception e) {
            result.put("success", false);
            result.put("message", "新增限购失败：" + e.getMessage());
            result.put("data", null);
            result.put("code", 500);
            // @Transactional注解会自动回滚事务
            throw e; // 重新抛出异常以触发事务回滚
        }

        return result;
    }

    /**
     * 解除限购测试接口 - 不操作数据库，仅测试数据接收
     * @param request 解除限购测试请求参数
     * @return 返回解除限购测试结果
     */
    @PostMapping("/cc")
    @ResponseBody
    public Map<String, Object> relieveLimitTest(@RequestBody RelieveLimitTestRequest request) {
        Map<String, Object> result = new HashMap<>();
        try {
            LOG.info("开始处理解除限购测试请求");

            // 打印接收到的参数
            LOG.info("接收到的请求参数：{}", request.toString());

            // 解除限购的基本信息
            LOG.info("限购记录ID：{}", request.getFLimitId());
            LOG.info("解除原因：{}", request.getRemove());
            LOG.info("其他原因：{}", request.getFVoidRemarks());

            // 显示的限购信息（只读）
            LOG.info("限购类型：{}", request.getFLimitStyle());
            LOG.info("限制周期：{}", request.getFTimeType());
            LOG.info("支付限制：{}", request.getFLimitType());

            // 操作员信息字段
            LOG.info("组织ID：{}", request.getOrgId());
            LOG.info("组织名称：{}", request.getOrgName());
            LOG.info("部门ID：{}", request.getDepId());
            LOG.info("部门名称：{}", request.getDepName());
            LOG.info("操作员：{}", request.getOperator());
            LOG.info("操作员ID：{}", request.getOperatorId());
            LOG.info("操作时间：{}", request.getOperateDate());

            // 生成测试返回数据
            LocalDateTime now = LocalDateTime.now();
            String timestamp = now.format(DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm:ss'Z'"));

            Map<String, Object> data = new HashMap<>();
            data.put("limit_id", request.getFLimitId());
            data.put("remove_reason", request.getRemove());
            data.put("void_remarks", request.getFVoidRemarks());

            // 构建操作员信息，处理null值
            Map<String, Object> operatorInfo = new HashMap<>();
            operatorInfo.put("orgId", request.getOrgId() != null ? request.getOrgId() : "");
            operatorInfo.put("orgName", request.getOrgName() != null ? request.getOrgName() : "");
            operatorInfo.put("depId", request.getDepId() != null ? request.getDepId() : "");
            operatorInfo.put("depName", request.getDepName() != null ? request.getDepName() : "");
            operatorInfo.put("operator", request.getOperator() != null ? request.getOperator() : "");
            operatorInfo.put("operatorId", request.getOperatorId() != null ? request.getOperatorId() : "");
            operatorInfo.put("operateDate", request.getOperateDate() != null ? request.getOperateDate() : "");
            data.put("operator_info", operatorInfo);

            // 构建限购信息，处理null值
            Map<String, Object> limitInfo = new HashMap<>();
            limitInfo.put("limit_style", request.getFLimitStyle() != null ? request.getFLimitStyle() : "");
            limitInfo.put("time_type", request.getFTimeType() != null ? request.getFTimeType() : "");
            limitInfo.put("limit_type", request.getFLimitType() != null ? request.getFLimitType() : "");
            data.put("limit_info", limitInfo);

            result.put("success", true);
            result.put("message", "解除限购测试成功 - 数据接收正常");
            result.put("data", data);
            result.put("code", 200);
            result.put("timestamp", timestamp);

            LOG.info("解除限购测试处理完成");

        } catch (Exception e) {
            LOG.error("解除限购测试处理失败", e);
            result.put("success", false);
            result.put("message", "解除限购测试失败：" + e.getMessage());
            result.put("data", null);
            result.put("code", 500);
            result.put("timestamp", LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm:ss'Z'")));
        }

        return result;
    }

    /**
     * 新增限购测试接口 - 不操作数据库，仅测试数据接收
     * @param request 新增限购测试请求参数
     * @return 返回新增限购测试结果
     */
    @PostMapping("/vv")
    @ResponseBody
    public Map<String, Object> addLimitGasTest(@RequestBody AddLimitGasTestRequest request) {
        Map<String, Object> result = new HashMap<>();
        try {
            LOG.info("开始处理新增限购测试请求");

            // 打印接收到的参数
            LOG.info("接收到的请求参数：{}", request.toString());

            // 表单输入的限购信息
            LOG.info("限购类型：{}", request.getFLimitStyle());
            LOG.info("限制周期：{}", request.getFTimeType());
            LOG.info("支付限制：{}", request.getFLimitType());
            LOG.info("执行时间：{}", request.getFStartDate());
            LOG.info("单次限购：{}", request.getFTimeValue());
            LOG.info("支持第三方缴费：{}", request.getFThirdPay());
            LOG.info("限购次数：{}", request.getFLimitTimes());

            // 用户信息ID
            LOG.info("用户信息ID：{}", request.getFUserinfoId());

            // 操作员信息字段
            LOG.info("组织ID：{}", request.getOrgId());
            LOG.info("组织名称：{}", request.getOrgName());
            LOG.info("部门ID：{}", request.getDepId());
            LOG.info("部门名称：{}", request.getDepName());
            LOG.info("操作员：{}", request.getOperator());
            LOG.info("操作员ID：{}", request.getOperatorId());
            LOG.info("操作时间：{}", request.getOperateDate());

            // 生成测试返回数据
            LocalDateTime now = LocalDateTime.now();
            String timestamp = now.format(DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm:ss'Z'"));

            Map<String, Object> data = new HashMap<>();
            data.put("userinfo_id", request.getFUserinfoId());

            // 构建限购信息，处理null值
            Map<String, Object> limitInfo = new HashMap<>();
            limitInfo.put("limit_style", request.getFLimitStyle() != null ? request.getFLimitStyle() : "");
            limitInfo.put("time_type", request.getFTimeType() != null ? request.getFTimeType() : "");
            limitInfo.put("limit_type", request.getFLimitType() != null ? request.getFLimitType() : "");
            limitInfo.put("start_date", request.getFStartDate() != null ? request.getFStartDate() : "");
            limitInfo.put("time_value", request.getFTimeValue() != null ? request.getFTimeValue() : "");
            limitInfo.put("third_pay", request.getFThirdPay() != null ? request.getFThirdPay() : "");
            limitInfo.put("limit_times", request.getFLimitTimes() != null ? request.getFLimitTimes() : "");
            data.put("limit_info", limitInfo);

            // 构建操作员信息，处理null值
            Map<String, Object> operatorInfo = new HashMap<>();
            operatorInfo.put("orgId", request.getOrgId() != null ? request.getOrgId() : "");
            operatorInfo.put("orgName", request.getOrgName() != null ? request.getOrgName() : "");
            operatorInfo.put("depId", request.getDepId() != null ? request.getDepId() : "");
            operatorInfo.put("depName", request.getDepName() != null ? request.getDepName() : "");
            operatorInfo.put("operator", request.getOperator() != null ? request.getOperator() : "");
            operatorInfo.put("operatorId", request.getOperatorId() != null ? request.getOperatorId() : "");
            operatorInfo.put("operateDate", request.getOperateDate() != null ? request.getOperateDate() : "");
            data.put("operator_info", operatorInfo);

            result.put("success", true);
            result.put("message", "新增限购测试成功 - 数据接收正常");
            result.put("data", data);
            result.put("code", 200);
            result.put("timestamp", timestamp);

            LOG.info("新增限购测试处理完成");

        } catch (Exception e) {
            LOG.error("新增限购测试处理失败", e);
            result.put("success", false);
            result.put("message", "新增限购测试失败：" + e.getMessage());
            result.put("data", null);
            result.put("code", 500);
            result.put("timestamp", LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm:ss'Z'")));
        }

        return result;
    }
}
