package com.af.v4.safecheck.controller;

import com.af.v4.safecheck.entity.LimitGas;
import com.af.v4.system.common.jpa.service.SqlService;
import org.json.JSONArray;
import org.json.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.*;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.HashMap;
import java.util.Map;
import java.util.UUID;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import com.af.v4.safecheck.dto.RelieveLimitRequest;
import com.af.v4.safecheck.dto.AddLimitGasRequest;
import com.af.v4.safecheck.dto.RelieveLimitTestRequest;
import com.af.v4.safecheck.dto.AddLimitGasTestRequest;

/**
 * 限气表控制器
 * 用于查询返回字段，条件：f_State = '有效'
 */
@RestController
@RequestMapping("/limitgas")
public class LimitGasController {

    private static final Logger LOG = LoggerFactory.getLogger(LimitGasController.class);
    private final SqlService sqlService;

    public LimitGasController(SqlService sqlService) {
        this.sqlService = sqlService;
    }

    /**
     * 查询所有有效的限气记录
     * @return 返回有效状态的限气记录列表
     */
    @GetMapping("/valid")
    @ResponseBody
    public Map<String, Object> getValidLimitGas() {
        Map<String, Object> result = new HashMap<>();
        try {
            LOG.info("开始查询有效的限气记录");

            // 构建查询SQL（尝试不同的字段名）
            String sql = "SELECT * FROM t_limit_gas WHERE f_State = '有效' ORDER BY f_operate_date DESC";

            // 执行查询
            JSONArray queryResult = sqlService.querySQL("查询有效限气记录", sql);

            result.put("code", 200);
            result.put("message", "查询成功");
        } catch (Exception e) {
            LOG.error("查询有效限气记录失败", e);
            result.put("code", 500);
            result.put("message", "查询失败：" + e.getMessage());
            result.put("data", new ArrayList<>());
            result.put("total", 0);
        }

        return result;
    }

    /**
     * 解除限购接口
     * @param request 解除限购请求参数
     * @return 返回解除限购结果
     */
    @PostMapping("/cc1")
    @ResponseBody
    public Map<String, Object> relieveLimit(@RequestBody RelieveLimitRequest request) {
        Map<String, Object> result = new HashMap<>();
        try {
            LOG.info("开始处理解除限购请求");

            // 打印接收到的参数
            LOG.info("接收到的请求参数：{}", request.toString());
            LOG.info("解除原因：{}", request.getRemove());
            LOG.info("其他原因：{}", request.getF_void_remarks());
            LOG.info("修改用户ID：{}", request.getF_modify_user_id());
            LOG.info("修改用户姓名：{}", request.getF_modify_user_name());
            LOG.info("修改时间：{}", request.getF_modify_date());
            LOG.info("限购记录ID：{}", request.getF_limit_id());
            LOG.info("限购类型：{}", request.getF_limit_style());
            LOG.info("限制周期：{}", request.getF_time_type());
            LOG.info("支付限制：{}", request.getF_limit_type());

            // 1. 先查询当前记录是否存在且状态为有效
            String queryLimitSql = "SELECT * FROM t_limit_gas WHERE id = " + request.getF_limit_id() + " AND f_State = '有效'";
            JSONArray queryResult = sqlService.querySQL("查询限购记录", queryLimitSql);

            if (queryResult.length() == 0) {
                result.put("code", 404);
                result.put("message", "未找到有效的限购记录，记录ID：" + request.getF_limit_id());
                result.put("success", false);
                LOG.warn("未找到有效的限购记录，ID：{}", request.getF_limit_id());
                return result;
            }

            LOG.info("找到有效限购记录，准备更新状态");

            // 2. 更新记录状态为无效
            String updateSql = "UPDATE t_limit_gas SET f_State = '无效' WHERE id = " + request.getF_limit_id();

            LOG.info("执行更新SQL：{}", updateSql);

            // 执行更新操作
            try {
                sqlService.execSQL("更新限购记录状态", updateSql);
                LOG.info("限购记录状态更新成功，ID：{}", request.getF_limit_id());
            } catch (Exception updateEx) {
                LOG.error("更新限购记录状态失败", updateEx);
                result.put("code", 500);
                result.put("message", "更新限购记录状态失败：" + updateEx.getMessage());
                result.put("success", false);
                return result;
            }

            // 3. 生成解除ID
            String relieveId = "relieve_" + UUID.randomUUID().toString().replace("-", "").substring(0, 6);

            // 4. 构建返回数据
            Map<String, Object> data = new HashMap<>();
            data.put("relieveId", relieveId);
            data.put("limitId", request.getF_limit_id());
            data.put("relieveTime", request.getF_modify_date());
            data.put("relieveUser", request.getF_modify_user_name());
            data.put("status", "已解除");

            result.put("code", 200);
            result.put("message", "解除限购成功");
            result.put("data", data);
            result.put("success", true);

            LOG.info("解除限购处理完成，解除ID：{}，限购记录ID：{}", relieveId, request.getF_limit_id());

        } catch (Exception e) {
            LOG.error("解除限购处理失败", e);
            result.put("code", 500);
            result.put("message", "解除限购失败：" + e.getMessage());
            result.put("success", false);
        }

        return result;
    }

    /**
     * 新增限购接口
     * @param request 新增限购请求参数
     * @return 返回新增限购结果
     */
    @PostMapping("/vv1")
    @ResponseBody
    public Map<String, Object> addLimitGas(@RequestBody AddLimitGasRequest request) {
        Map<String, Object> result = new HashMap<>();
        try {
            LOG.info("开始处理新增限购请求");

            // 打印接收到的参数
            LOG.info("接收到的请求参数：{}", request.toString());
            LOG.info("限购类型：{}", request.getFLimitStyle());
            LOG.info("限制周期：{}", request.getFTimeType());
            LOG.info("支付限制：{}", request.getFLimitType());
            LOG.info("执行时间：{}", request.getFStartDate());
            LOG.info("单次限购：{}", request.getFTimeValue());
            LOG.info("支持第三方缴费：{}", request.getFThirdPay());
            LOG.info("限购次数：{}", request.getFLimitTimes());
            LOG.info("操作时间：{}", request.getFOperateDate());
            LOG.info("用户信息ID：{}", request.getFUserinfoId());
            LOG.info("资源用户ID：{}", request.getFResourcesId());
            LOG.info("资源用户名称：{}", request.getFResourcesName());

            // 验证必要参数
            if (request.getFUserinfoId() == null || request.getFUserinfoId().trim().isEmpty()) {
                result.put("success", false);
                result.put("message", "用户信息ID不能为空");
                result.put("code", 400);
                return result;
            }

            // 1. 先查询该用户是否已存在有效的限气记录
            String queryExistingSql = "SELECT * FROM t_limit_gas WHERE f_userinfo_id = " + request.getFUserinfoId() + " AND f_State = '有效'";
            LOG.info("查询现有限气记录SQL：{}", queryExistingSql);

            JSONArray existingRecords = sqlService.querySQL("查询现有限气记录", queryExistingSql);

            boolean isUpdate = existingRecords.length() > 0;
            String operationType = isUpdate ? "更新" : "新增";
            LOG.info("用户ID {} 的限气记录{}操作", request.getFUserinfoId(), operationType);

            // 2. 准备数据库操作的SQL
            String sql;
            if (isUpdate) {
                // 更新现有记录
                JSONObject existingRecord = existingRecords.getJSONObject(0);
                Integer recordId = existingRecord.getInt("id");

                sql = "UPDATE t_limit_gas SET " +
                      "f_limit_style = '" + (request.getFLimitStyle() != null ? request.getFLimitStyle() : "") + "', " +
                      "f_time_type = '" + (request.getFTimeType() != null ? request.getFTimeType() : "") + "', " +
                      "f_limit_type = '" + (request.getFLimitType() != null ? request.getFLimitType() : "") + "', " +
                      "f_start_date = '" + (request.getFStartDate() != null ? request.getFStartDate() : "") + "', " +
                      "f_time_value = '" + (request.getFTimeValue() != null ? request.getFTimeValue() : "") + "', " +
                      "f_third_pay = " + (request.getFThirdPay() != null && !request.getFThirdPay().trim().isEmpty() ? request.getFThirdPay() : "0") + ", " +
                      "f_limit_times = '" + (request.getFLimitTimes() != null ? request.getFLimitTimes() : "") + "', " +
                      "f_operate_date = '" + (request.getFOperateDate() != null ? request.getFOperateDate() : LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"))) + "', " +
                      "f_operate_people = '" + (request.getFResourcesName() != null ? request.getFResourcesName() : "") + "', " +
                      "f_operate_peopleid = '" + (request.getFResourcesId() != null ? request.getFResourcesId() : "") + "' " +
                      "WHERE id = " + recordId;
            } else {
                // 插入新记录
                sql = "INSERT INTO t_limit_gas (" +
                      "f_userinfo_id, f_limit_style, f_time_type, f_limit_type, f_start_date, " +
                      "f_time_value, f_third_pay, f_limit_times, f_operate_date, f_operate_people, " +
                      "f_operate_peopleid, f_state" +
                      ") VALUES (" +
                      request.getFUserinfoId() + ", " +
                      "'" + (request.getFLimitStyle() != null ? request.getFLimitStyle() : "") + "', " +
                      "'" + (request.getFTimeType() != null ? request.getFTimeType() : "") + "', " +
                      "'" + (request.getFLimitType() != null ? request.getFLimitType() : "") + "', " +
                      "'" + (request.getFStartDate() != null ? request.getFStartDate() : "") + "', " +
                      "'" + (request.getFTimeValue() != null ? request.getFTimeValue() : "") + "', " +
                      (request.getFThirdPay() != null && !request.getFThirdPay().trim().isEmpty() ? request.getFThirdPay() : "0") + ", " +
                      "'" + (request.getFLimitTimes() != null ? request.getFLimitTimes() : "") + "', " +
                      "'" + (request.getFOperateDate() != null ? request.getFOperateDate() : LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"))) + "', " +
                      "'" + (request.getFResourcesName() != null ? request.getFResourcesName() : "") + "', " +
                      "'" + (request.getFResourcesId() != null ? request.getFResourcesId() : "") + "', " +
                      "'有效'" +
                      ")";
            }

            LOG.info("执行{}SQL：{}", operationType, sql);

            // 3. 执行数据库操作
            try {
                sqlService.execSQL(operationType + "限气记录", sql);
                LOG.info("限气记录{}成功，用户ID：{}", operationType, request.getFUserinfoId());
            } catch (Exception dbEx) {
                LOG.error("限气记录{}失败", operationType, dbEx);
                result.put("success", false);
                result.put("message", operationType + "限气记录失败：" + dbEx.getMessage());
                result.put("code", 500);
                return result;
            }

            // 4. 生成返回数据
            LocalDateTime now = LocalDateTime.now();
            String createdAt = now.format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
            String timestamp = now.format(DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm:ss'Z'"));

            Map<String, Object> data = new HashMap<>();
            data.put("userinfo_id", request.getFUserinfoId());
            data.put("operation_type", operationType);
            data.put("created_at", createdAt);
            data.put("limit_style", request.getFLimitStyle());
            data.put("time_type", request.getFTimeType());
            data.put("limit_type", request.getFLimitType());

            result.put("success", true);
            result.put("message", operationType + "限气记录成功");
            result.put("data", data);
            result.put("code", 200);
            result.put("timestamp", timestamp);

            LOG.info("限气记录{}处理完成，用户ID：{}", operationType, request.getFUserinfoId());

        } catch (Exception e) {
            LOG.error("新增限购处理失败", e);
            result.put("success", false);
            result.put("message", "新增限购失败：" + e.getMessage());
            result.put("data", null);
            result.put("code", 500);
            result.put("timestamp", LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm:ss'Z'")));
        }

        return result;
    }

    /**
     * 解除限购测试接口 - 不操作数据库，仅测试数据接收
     * @param request 解除限购测试请求参数
     * @return 返回解除限购测试结果
     */
    @PostMapping("/cc")
    @ResponseBody
    public Map<String, Object> relieveLimitTest(@RequestBody RelieveLimitTestRequest request) {
        Map<String, Object> result = new HashMap<>();
        try {
            LOG.info("开始处理解除限购测试请求");

            // 打印接收到的参数
            LOG.info("接收到的请求参数：{}", request.toString());

            // 解除限购的基本信息
            LOG.info("限购记录ID：{}", request.getFLimitId());
            LOG.info("解除原因：{}", request.getRemove());
            LOG.info("其他原因：{}", request.getFVoidRemarks());

            // 显示的限购信息（只读）
            LOG.info("限购类型：{}", request.getFLimitStyle());
            LOG.info("限制周期：{}", request.getFTimeType());
            LOG.info("支付限制：{}", request.getFLimitType());

            // 操作员信息字段
            LOG.info("组织ID：{}", request.getOrgId());
            LOG.info("组织名称：{}", request.getOrgName());
            LOG.info("部门ID：{}", request.getDepId());
            LOG.info("部门名称：{}", request.getDepName());
            LOG.info("操作员：{}", request.getOperator());
            LOG.info("操作员ID：{}", request.getOperatorId());
            LOG.info("操作时间：{}", request.getOperateDate());

            // 生成测试返回数据
            LocalDateTime now = LocalDateTime.now();
            String timestamp = now.format(DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm:ss'Z'"));

            Map<String, Object> data = new HashMap<>();
            data.put("limit_id", request.getFLimitId());
            data.put("remove_reason", request.getRemove());
            data.put("void_remarks", request.getFVoidRemarks());

            // 构建操作员信息，处理null值
            Map<String, Object> operatorInfo = new HashMap<>();
            operatorInfo.put("orgId", request.getOrgId() != null ? request.getOrgId() : "");
            operatorInfo.put("orgName", request.getOrgName() != null ? request.getOrgName() : "");
            operatorInfo.put("depId", request.getDepId() != null ? request.getDepId() : "");
            operatorInfo.put("depName", request.getDepName() != null ? request.getDepName() : "");
            operatorInfo.put("operator", request.getOperator() != null ? request.getOperator() : "");
            operatorInfo.put("operatorId", request.getOperatorId() != null ? request.getOperatorId() : "");
            operatorInfo.put("operateDate", request.getOperateDate() != null ? request.getOperateDate() : "");
            data.put("operator_info", operatorInfo);

            // 构建限购信息，处理null值
            Map<String, Object> limitInfo = new HashMap<>();
            limitInfo.put("limit_style", request.getFLimitStyle() != null ? request.getFLimitStyle() : "");
            limitInfo.put("time_type", request.getFTimeType() != null ? request.getFTimeType() : "");
            limitInfo.put("limit_type", request.getFLimitType() != null ? request.getFLimitType() : "");
            data.put("limit_info", limitInfo);

            result.put("success", true);
            result.put("message", "解除限购测试成功 - 数据接收正常");
            result.put("data", data);
            result.put("code", 200);
            result.put("timestamp", timestamp);

            LOG.info("解除限购测试处理完成");

        } catch (Exception e) {
            LOG.error("解除限购测试处理失败", e);
            result.put("success", false);
            result.put("message", "解除限购测试失败：" + e.getMessage());
            result.put("data", null);
            result.put("code", 500);
            result.put("timestamp", LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm:ss'Z'")));
        }

        return result;
    }

    /**
     * 新增限购测试接口 - 不操作数据库，仅测试数据接收
     * @param request 新增限购测试请求参数
     * @return 返回新增限购测试结果
     */
    @PostMapping("/vv")
    @ResponseBody
    public Map<String, Object> addLimitGasTest(@RequestBody AddLimitGasTestRequest request) {
        Map<String, Object> result = new HashMap<>();
        try {
            LOG.info("开始处理新增限购测试请求");

            // 打印接收到的参数
            LOG.info("接收到的请求参数：{}", request.toString());

            // 表单输入的限购信息
            LOG.info("限购类型：{}", request.getFLimitStyle());
            LOG.info("限制周期：{}", request.getFTimeType());
            LOG.info("支付限制：{}", request.getFLimitType());
            LOG.info("执行时间：{}", request.getFStartDate());
            LOG.info("单次限购：{}", request.getFTimeValue());
            LOG.info("支持第三方缴费：{}", request.getFThirdPay());
            LOG.info("限购次数：{}", request.getFLimitTimes());

            // 用户信息ID
            LOG.info("用户信息ID：{}", request.getFUserinfoId());

            // 操作员信息字段
            LOG.info("组织ID：{}", request.getOrgId());
            LOG.info("组织名称：{}", request.getOrgName());
            LOG.info("部门ID：{}", request.getDepId());
            LOG.info("部门名称：{}", request.getDepName());
            LOG.info("操作员：{}", request.getOperator());
            LOG.info("操作员ID：{}", request.getOperatorId());
            LOG.info("操作时间：{}", request.getOperateDate());

            // 生成测试返回数据
            LocalDateTime now = LocalDateTime.now();
            String timestamp = now.format(DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm:ss'Z'"));

            Map<String, Object> data = new HashMap<>();
            data.put("userinfo_id", request.getFUserinfoId());
            data.put("limit_info", Map.of(
                "limit_style", request.getFLimitStyle(),
                "time_type", request.getFTimeType(),
                "limit_type", request.getFLimitType(),
                "start_date", request.getFStartDate(),
                "time_value", request.getFTimeValue(),
                "third_pay", request.getFThirdPay(),
                "limit_times", request.getFLimitTimes()
            ));
            data.put("operator_info", Map.of(
                "orgId", request.getOrgId(),
                "orgName", request.getOrgName(),
                "depId", request.getDepId(),
                "depName", request.getDepName(),
                "operator", request.getOperator(),
                "operatorId", request.getOperatorId(),
                "operateDate", request.getOperateDate()
            ));

            result.put("success", true);
            result.put("message", "新增限购测试成功 - 数据接收正常");
            result.put("data", data);
            result.put("code", 200);
            result.put("timestamp", timestamp);

            LOG.info("新增限购测试处理完成");

        } catch (Exception e) {
            LOG.error("新增限购测试处理失败", e);
            result.put("success", false);
            result.put("message", "新增限购测试失败：" + e.getMessage());
            result.put("data", null);
            result.put("code", 500);
            result.put("timestamp", LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm:ss'Z'")));
        }

        return result;
    }
}
