package com.af.v4.safecheck.dto;

import com.fasterxml.jackson.annotation.JsonProperty;

/**
 * 新增限购测试请求对象
 */
public class AddLimitGasTestRequest {

    // 表单输入的限购信息
    @JsonProperty("f_limit_style")
    private String fLimitStyle;

    @JsonProperty("f_time_type")
    private String fTimeType;

    @JsonProperty("f_limit_type")
    private String fLimitType;

    @JsonProperty("f_start_date")
    private String fStartDate;

    @JsonProperty("f_time_value")
    private String fTimeValue;

    @JsonProperty("f_third_pay")
    private String fThirdPay;

    @JsonProperty("f_limit_times")
    private String fLimitTimes;

    // 用户信息ID
    @JsonProperty("f_userinfo_id")
    private String fUserinfoId;

    // 操作员信息字段（统一的操作员信息）
    @JsonProperty("orgId")
    private String orgId;

    @JsonProperty("orgName")
    private String orgName;

    @JsonProperty("depId")
    private String depId;

    @JsonProperty("depName")
    private String depName;

    @JsonProperty("operator")
    private String operator;

    @JsonProperty("operatorId")
    private String operatorId;

    @JsonProperty("operateDate")
    private String operateDate;

    // 无参构造函数
    public AddLimitGasTestRequest() {
    }

    // 全参构造函数
    public AddLimitGasTestRequest(String fLimitStyle, String fTimeType, String fLimitType,
                                  String fStartDate, String fTimeValue, String fThirdPay,
                                  String fLimitTimes, String fUserinfoId, String orgId,
                                  String orgName, String depId, String depName,
                                  String operator, String operatorId, String operateDate) {
        this.fLimitStyle = fLimitStyle;
        this.fTimeType = fTimeType;
        this.fLimitType = fLimitType;
        this.fStartDate = fStartDate;
        this.fTimeValue = fTimeValue;
        this.fThirdPay = fThirdPay;
        this.fLimitTimes = fLimitTimes;
        this.fUserinfoId = fUserinfoId;
        this.orgId = orgId;
        this.orgName = orgName;
        this.depId = depId;
        this.depName = depName;
        this.operator = operator;
        this.operatorId = operatorId;
        this.operateDate = operateDate;
    }

    // Getter 和 Setter 方法
    public String getFLimitStyle() {
        return fLimitStyle;
    }

    public void setFLimitStyle(String fLimitStyle) {
        this.fLimitStyle = fLimitStyle;
    }

    public String getFTimeType() {
        return fTimeType;
    }

    public void setFTimeType(String fTimeType) {
        this.fTimeType = fTimeType;
    }

    public String getFLimitType() {
        return fLimitType;
    }

    public void setFLimitType(String fLimitType) {
        this.fLimitType = fLimitType;
    }

    public String getFStartDate() {
        return fStartDate;
    }

    public void setFStartDate(String fStartDate) {
        this.fStartDate = fStartDate;
    }

    public String getFTimeValue() {
        return fTimeValue;
    }

    public void setFTimeValue(String fTimeValue) {
        this.fTimeValue = fTimeValue;
    }

    public String getFThirdPay() {
        return fThirdPay;
    }

    public void setFThirdPay(String fThirdPay) {
        this.fThirdPay = fThirdPay;
    }

    public String getFLimitTimes() {
        return fLimitTimes;
    }

    public void setFLimitTimes(String fLimitTimes) {
        this.fLimitTimes = fLimitTimes;
    }

    public String getFUserinfoId() {
        return fUserinfoId;
    }

    public void setFUserinfoId(String fUserinfoId) {
        this.fUserinfoId = fUserinfoId;
    }

    public String getOrgId() {
        return orgId;
    }

    public void setOrgId(String orgId) {
        this.orgId = orgId;
    }

    public String getOrgName() {
        return orgName;
    }

    public void setOrgName(String orgName) {
        this.orgName = orgName;
    }

    public String getDepId() {
        return depId;
    }

    public void setDepId(String depId) {
        this.depId = depId;
    }

    public String getDepName() {
        return depName;
    }

    public void setDepName(String depName) {
        this.depName = depName;
    }

    public String getOperator() {
        return operator;
    }

    public void setOperator(String operator) {
        this.operator = operator;
    }

    public String getOperatorId() {
        return operatorId;
    }

    public void setOperatorId(String operatorId) {
        this.operatorId = operatorId;
    }

    public String getOperateDate() {
        return operateDate;
    }

    public void setOperateDate(String operateDate) {
        this.operateDate = operateDate;
    }

    @Override
    public String toString() {
        return "AddLimitGasTestRequest{" +
                "fLimitStyle='" + fLimitStyle + '\'' +
                ", fTimeType='" + fTimeType + '\'' +
                ", fLimitType='" + fLimitType + '\'' +
                ", fStartDate='" + fStartDate + '\'' +
                ", fTimeValue='" + fTimeValue + '\'' +
                ", fThirdPay='" + fThirdPay + '\'' +
                ", fLimitTimes='" + fLimitTimes + '\'' +
                ", fUserinfoId='" + fUserinfoId + '\'' +
                ", orgId='" + orgId + '\'' +
                ", orgName='" + orgName + '\'' +
                ", depId='" + depId + '\'' +
                ", depName='" + depName + '\'' +
                ", operator='" + operator + '\'' +
                ", operatorId='" + operatorId + '\'' +
                ", operateDate='" + operateDate + '\'' +
                '}';
    }
}
