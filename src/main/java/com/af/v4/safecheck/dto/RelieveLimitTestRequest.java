package com.af.v4.safecheck.dto;

import com.fasterxml.jackson.annotation.JsonProperty;

/**
 * 解除限购测试请求对象
 */
public class RelieveLimitTestRequest {

    // 解除限购的基本信息
    @JsonProperty("f_limit_id")
    private String fLimitId;

    @JsonProperty("remove")
    private String remove;

    @JsonProperty("f_void_remarks")
    private String fVoidRemarks;

    // 显示的限购信息（只读）
    @JsonProperty("f_limit_style")
    private String fLimitStyle;

    @JsonProperty("f_time_type")
    private String fTimeType;

    @JsonProperty("f_limit_type")
    private String fLimitType;

    // 操作员信息字段（统一的操作员信息，包含修改人信息）
    @JsonProperty("orgId")
    private String orgId;

    @JsonProperty("orgName")
    private String orgName;

    @JsonProperty("depId")
    private String depId;

    @JsonProperty("depName")
    private String depName;

    @JsonProperty("operator")
    private String operator;

    @JsonProperty("operatorId")
    private String operatorId;

    @JsonProperty("operateDate")
    private String operateDate;

    // 无参构造函数
    public RelieveLimitTestRequest() {
    }

    // 全参构造函数
    public RelieveLimitTestRequest(String fLimitId, String remove, String fVoidRemarks,
                                   String fLimitStyle, String fTimeType, String fLimitType,
                                   String orgId, String orgName, String depId, String depName,
                                   String operator, String operatorId, String operateDate) {
        this.fLimitId = fLimitId;
        this.remove = remove;
        this.fVoidRemarks = fVoidRemarks;
        this.fLimitStyle = fLimitStyle;
        this.fTimeType = fTimeType;
        this.fLimitType = fLimitType;
        this.orgId = orgId;
        this.orgName = orgName;
        this.depId = depId;
        this.depName = depName;
        this.operator = operator;
        this.operatorId = operatorId;
        this.operateDate = operateDate;
    }

    // Getter 和 Setter 方法
    public String getFLimitId() {
        return fLimitId;
    }

    public void setFLimitId(String fLimitId) {
        this.fLimitId = fLimitId;
    }

    public String getRemove() {
        return remove;
    }

    public void setRemove(String remove) {
        this.remove = remove;
    }

    public String getFVoidRemarks() {
        return fVoidRemarks;
    }

    public void setFVoidRemarks(String fVoidRemarks) {
        this.fVoidRemarks = fVoidRemarks;
    }

    public String getFLimitStyle() {
        return fLimitStyle;
    }

    public void setFLimitStyle(String fLimitStyle) {
        this.fLimitStyle = fLimitStyle;
    }

    public String getFTimeType() {
        return fTimeType;
    }

    public void setFTimeType(String fTimeType) {
        this.fTimeType = fTimeType;
    }

    public String getFLimitType() {
        return fLimitType;
    }

    public void setFLimitType(String fLimitType) {
        this.fLimitType = fLimitType;
    }

    public String getOrgId() {
        return orgId;
    }

    public void setOrgId(String orgId) {
        this.orgId = orgId;
    }

    public String getOrgName() {
        return orgName;
    }

    public void setOrgName(String orgName) {
        this.orgName = orgName;
    }

    public String getDepId() {
        return depId;
    }

    public void setDepId(String depId) {
        this.depId = depId;
    }

    public String getDepName() {
        return depName;
    }

    public void setDepName(String depName) {
        this.depName = depName;
    }

    public String getOperator() {
        return operator;
    }

    public void setOperator(String operator) {
        this.operator = operator;
    }

    public String getOperatorId() {
        return operatorId;
    }

    public void setOperatorId(String operatorId) {
        this.operatorId = operatorId;
    }

    public String getOperateDate() {
        return operateDate;
    }

    public void setOperateDate(String operateDate) {
        this.operateDate = operateDate;
    }

    @Override
    public String toString() {
        return "RelieveLimitTestRequest{" +
                "fLimitId='" + fLimitId + '\'' +
                ", remove='" + remove + '\'' +
                ", fVoidRemarks='" + fVoidRemarks + '\'' +
                ", fLimitStyle='" + fLimitStyle + '\'' +
                ", fTimeType='" + fTimeType + '\'' +
                ", fLimitType='" + fLimitType + '\'' +
                ", orgId='" + orgId + '\'' +
                ", orgName='" + orgName + '\'' +
                ", depId='" + depId + '\'' +
                ", depName='" + depName + '\'' +
                ", operator='" + operator + '\'' +
                ", operatorId='" + operatorId + '\'' +
                ", operateDate='" + operateDate + '\'' +
                '}';
    }
}
