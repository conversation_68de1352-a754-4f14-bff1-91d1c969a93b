package com.af.v4.safecheck.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ContentStyle;
import com.alibaba.excel.converters.string.StringImageConverter;
import com.alibaba.excel.enums.poi.BorderStyleEnum;
import com.alibaba.excel.enums.poi.VerticalAlignmentEnum;

@ContentStyle(verticalAlignment = VerticalAlignmentEnum.CENTER,borderBottom = BorderStyleEnum.THIN,
        borderLeft = BorderStyleEnum.THIN,borderRight = BorderStyleEnum.THIN,borderTop = BorderStyleEnum.THIN
)
public class ExportDefectNewDTO {

    @ExcelProperty(converter = StringImageConverter.class)
    private String f_path;
    @ExcelProperty(converter = StringImageConverter.class)
    private String f_p1_path;
    @ExcelProperty(converter = StringImageConverter.class)
    private String f_p2_path;
    @ExcelProperty(converter = StringImageConverter.class)
    private String f_repair_path;
    @ExcelProperty(converter = StringImageConverter.class)
    private String f_repair1_path;
    @ExcelProperty(converter = StringImageConverter.class)
    private String f_repair2_path;
    @ExcelProperty(converter = StringImageConverter.class)
    private String f_img_url;

    public String getF_repair1_path() {
        return f_repair1_path;
    }

    public void setF_repair1_path(String f_repair1_path) {
        this.f_repair1_path = f_repair1_path;
    }

    public String getF_repair2_path() {
        return f_repair2_path;
    }

    public void setF_repair2_path(String f_repair2_path) {
        this.f_repair2_path = f_repair2_path;
    }

    public String getF_img_url() {
        return f_img_url;
    }

    public void setF_img_url(String f_img_url) {
        this.f_img_url = f_img_url;
    }

    public String getF_path() {
        return f_path;
    }

    public void setF_path(String f_path) {
        this.f_path = f_path;
    }

    public String getF_p1_path() {
        return f_p1_path;
    }

    public void setF_p1_path(String f_p1_path) {
        this.f_p1_path = f_p1_path;
    }

    public String getF_p2_path() {
        return f_p2_path;
    }

    public void setF_p2_path(String f_p2_path) {
        this.f_p2_path = f_p2_path;
    }

    public String getF_repair_path() {
        return f_repair_path;
    }

    public void setF_repair_path(String f_repair_path) {
        this.f_repair_path = f_repair_path;
    }
/*public static ExportDefectTestDTO fromMap(Map map) {
        ExportDefectTestDTO e = new ExportDefectTestDTO();
        e.setUserName((String) map.get("f_user_name"));
        e.setUserinfoId((String) map.get("f_userinfo_id"));
        e.setPath((String) map.get("f_path"));
        return e;
    }*/
}
