package com.af.v4.safecheck.entity;

import java.util.Date;

/**
 * 操作记录表实体类
 * 对应数据库表: t_record
 */
public class Record {
    
    /**
     * 主键ID
     */
    private Integer id;
    
    /**
     * 操作类型
     */
    private String fOperatType;
    
    /**
     * 操作描述
     */
    private String fDescribe;
    
    /**
     * 状态
     */
    private String fState;
    
    /**
     * 备注
     */
    private String fComments;
    
    /**
     * 操作时间
     */
    private Date fOperateDate;
    
    /**
     * 用户档案ID
     */
    private Integer fUserfilesId;
    
    /**
     * 用户信息ID
     */
    private Integer fUserinfoId;
    
    /**
     * 用户ID
     */
    private String fUserId;
    
    /**
     * 组织ID
     */
    private String fOrgid;
    
    /**
     * 组织名称
     */
    private String fOrgname;
    
    /**
     * 部门ID
     */
    private String fDepid;
    
    /**
     * 部门名称
     */
    private String fDepname;
    
    /**
     * 操作员ID
     */
    private String fOperatorid;
    
    /**
     * 操作员
     */
    private String fOperator;
    
    /**
     * 取消记录ID
     */
    private Integer fCancelRecordId;
    
    /**
     * 取消原因
     */
    private String fCancelReason;
    
    /**
     * 发送成功
     */
    private String fSendsuccess;
    
    /**
     * 发送短信
     */
    private String fSendSms;
    
    /**
     * 同步状态
     */
    private String fSynchState;
    
    /**
     * 分公司
     */
    private String fFiliale;
    
    /**
     * 营业网点
     */
    private String fOutlets;
    
    /**
     * 组织结构
     */
    private String fOrgstr;
    
    /**
     * 分公司IDs
     */
    private String fFilialeids;
    
    /**
     * 信息变更来源
     */
    private String fInfochangeSource;
    
    /**
     * 订单ID
     */
    private String fOrderid;
    
    /**
     * 用户姓名
     */
    private String fUserName;
    
    /**
     * 用户电话
     */
    private String fUserPhone;
    
    /**
     * 推送状态
     */
    private String fPushState;

    // 构造函数
    public Record() {
    }

    // Getter 和 Setter 方法
    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getfOperatType() {
        return fOperatType;
    }

    public void setfOperatType(String fOperatType) {
        this.fOperatType = fOperatType;
    }

    public String getfDescribe() {
        return fDescribe;
    }

    public void setfDescribe(String fDescribe) {
        this.fDescribe = fDescribe;
    }

    public String getfState() {
        return fState;
    }

    public void setfState(String fState) {
        this.fState = fState;
    }

    public String getfComments() {
        return fComments;
    }

    public void setfComments(String fComments) {
        this.fComments = fComments;
    }

    public Date getfOperateDate() {
        return fOperateDate;
    }

    public void setfOperateDate(Date fOperateDate) {
        this.fOperateDate = fOperateDate;
    }

    public Integer getfUserfilesId() {
        return fUserfilesId;
    }

    public void setfUserfilesId(Integer fUserfilesId) {
        this.fUserfilesId = fUserfilesId;
    }

    public Integer getfUserinfoId() {
        return fUserinfoId;
    }

    public void setfUserinfoId(Integer fUserinfoId) {
        this.fUserinfoId = fUserinfoId;
    }

    public String getfUserId() {
        return fUserId;
    }

    public void setfUserId(String fUserId) {
        this.fUserId = fUserId;
    }

    public String getfOrgid() {
        return fOrgid;
    }

    public void setfOrgid(String fOrgid) {
        this.fOrgid = fOrgid;
    }

    public String getfOrgname() {
        return fOrgname;
    }

    public void setfOrgname(String fOrgname) {
        this.fOrgname = fOrgname;
    }

    public String getfDepid() {
        return fDepid;
    }

    public void setfDepid(String fDepid) {
        this.fDepid = fDepid;
    }

    public String getfDepname() {
        return fDepname;
    }

    public void setfDepname(String fDepname) {
        this.fDepname = fDepname;
    }

    public String getfOperatorid() {
        return fOperatorid;
    }

    public void setfOperatorid(String fOperatorid) {
        this.fOperatorid = fOperatorid;
    }

    public String getfOperator() {
        return fOperator;
    }

    public void setfOperator(String fOperator) {
        this.fOperator = fOperator;
    }

    public Integer getfCancelRecordId() {
        return fCancelRecordId;
    }

    public void setfCancelRecordId(Integer fCancelRecordId) {
        this.fCancelRecordId = fCancelRecordId;
    }

    public String getfCancelReason() {
        return fCancelReason;
    }

    public void setfCancelReason(String fCancelReason) {
        this.fCancelReason = fCancelReason;
    }

    public String getfSendsuccess() {
        return fSendsuccess;
    }

    public void setfSendsuccess(String fSendsuccess) {
        this.fSendsuccess = fSendsuccess;
    }

    public String getfSendSms() {
        return fSendSms;
    }

    public void setfSendSms(String fSendSms) {
        this.fSendSms = fSendSms;
    }

    public String getfSynchState() {
        return fSynchState;
    }

    public void setfSynchState(String fSynchState) {
        this.fSynchState = fSynchState;
    }

    public String getfFiliale() {
        return fFiliale;
    }

    public void setfFiliale(String fFiliale) {
        this.fFiliale = fFiliale;
    }

    public String getfOutlets() {
        return fOutlets;
    }

    public void setfOutlets(String fOutlets) {
        this.fOutlets = fOutlets;
    }

    public String getfOrgstr() {
        return fOrgstr;
    }

    public void setfOrgstr(String fOrgstr) {
        this.fOrgstr = fOrgstr;
    }

    public String getfFilialeids() {
        return fFilialeids;
    }

    public void setfFilialeids(String fFilialeids) {
        this.fFilialeids = fFilialeids;
    }

    public String getfInfochangeSource() {
        return fInfochangeSource;
    }

    public void setfInfochangeSource(String fInfochangeSource) {
        this.fInfochangeSource = fInfochangeSource;
    }

    public String getfOrderid() {
        return fOrderid;
    }

    public void setfOrderid(String fOrderid) {
        this.fOrderid = fOrderid;
    }

    public String getfUserName() {
        return fUserName;
    }

    public void setfUserName(String fUserName) {
        this.fUserName = fUserName;
    }

    public String getfUserPhone() {
        return fUserPhone;
    }

    public void setfUserPhone(String fUserPhone) {
        this.fUserPhone = fUserPhone;
    }

    public String getfPushState() {
        return fPushState;
    }

    public void setfPushState(String fPushState) {
        this.fPushState = fPushState;
    }
}
