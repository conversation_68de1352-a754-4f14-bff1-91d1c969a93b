# 限购功能数据库操作逻辑和事务方法

## 1. cc1接口 - 解除限购操作

### 业务逻辑
1. 根据限购ID修改t_limit_gas表的f_State='无效'
2. 查询用户信息用于构建操作描述
3. 在t_record表新增一条"解除限购"记录
4. 获取新记录ID，更新t_limit_gas的f_cancle_record_id

### 事务方法
使用Spring的@Transactional注解确保数据一致性：
```java
@Transactional(rollbackFor = Exception.class)
```

### SQL操作序列
```sql
-- 步骤1：更新限购状态
UPDATE t_limit_gas SET f_State = '无效' WHERE id = {limitId}

-- 步骤2：查询用户信息
SELECT lg.f_userinfo_id, ui.f_user_name, uf.f_userfiles_id 
FROM t_limit_gas lg 
LEFT JOIN t_userinfo ui ON lg.f_userinfo_id = ui.f_userinfo_id 
LEFT JOIN t_userfiles uf ON lg.f_userinfo_id = uf.f_userinfo_id 
WHERE lg.id = {limitId}

-- 步骤3：插入操作记录
INSERT INTO t_record (
    f_operat_type, f_describe, f_state, f_comments, f_operate_date,
    f_userinfo_id, f_user_id, f_orgid, f_orgname, f_depid, f_depname,
    f_operatorid, f_operator, f_user_name
) VALUES (
    '解除限购', 
    '{操作员}对客户{用户名}进行解除限购操作',
    '有效',
    '{解除原因}',
    '{操作时间}',
    {用户信息ID},
    '{用户ID}',
    '{组织ID}',
    '{组织名称}',
    '{部门ID}',
    '{部门名称}',
    '{操作员ID}',
    '{操作员姓名}',
    '{用户姓名}'
)

-- 步骤4：更新关联记录ID
SELECT TOP 1 id FROM t_record 
WHERE f_operat_type = '解除限购' AND f_user_id = '{用户ID}' 
ORDER BY id DESC

UPDATE t_limit_gas SET f_cancle_record_id = {记录ID} 
WHERE id = {limitId}
```

## 2. vv1接口 - 新增限购操作

### 业务逻辑
1. 根据f_userinfo_id查询t_limit_gas表（查询所有记录，不限制状态）
2. 如果存在记录 → 更新现有记录，把f_state设为'有效'
3. 如果不存在记录 → 新增记录，状态为'有效'
4. 在t_record表新增操作记录（"新增限购"或"修改限购"）
5. 获取新记录ID，更新t_limit_gas的f_limit_recod_id

### 事务方法
使用Spring的@Transactional注解确保数据一致性：
```java
@Transactional(rollbackFor = Exception.class)
```

### SQL操作序列

#### 场景1：用户已有限购记录（更新操作）
```sql
-- 步骤1：查询现有记录
SELECT * FROM t_limit_gas WHERE f_userinfo_id = {用户ID}

-- 步骤2：更新现有记录
UPDATE t_limit_gas SET 
    f_limit_style = '{限购类型}',
    f_time_type = '{限购周期}',
    f_limit_type = '{支付限制}',
    f_start_date = '{执行时间}',
    f_third_pay = {第三方缴费},
    f_limit_times = '{限购次数}',
    f_operate_date = '{操作时间}',
    f_operate_people = '{操作员}',
    f_operate_peopleid = '{操作员ID}',
    f_state = '有效'
WHERE id = {限购记录ID}

-- 步骤3：插入操作记录
INSERT INTO t_record (
    f_operat_type, f_describe, f_state, f_comments, f_operate_date,
    f_userinfo_id, f_user_id, f_orgid, f_orgname, f_depid, f_depname,
    f_operatorid, f_operator, f_user_name
) VALUES (
    '修改限购',
    '{操作员}对客户{用户名}进行修改限购操作',
    '有效',
    '',
    '{操作时间}',
    {用户信息ID},
    '{用户ID}',
    '{组织ID}',
    '{组织名称}',
    '{部门ID}',
    '{部门名称}',
    '{操作员ID}',
    '{操作员姓名}',
    '{用户姓名}'
)

-- 步骤4：更新关联记录ID
SELECT TOP 1 id FROM t_record 
WHERE f_operat_type = '修改限购' AND f_user_id = '{用户ID}' 
ORDER BY id DESC

UPDATE t_limit_gas SET f_limit_recod_id = {记录ID} 
WHERE id = {限购记录ID}
```

#### 场景2：用户首次新增限购（插入操作）
```sql
-- 步骤1：查询现有记录
SELECT * FROM t_limit_gas WHERE f_userinfo_id = {用户ID}

-- 步骤2：插入新记录
INSERT INTO t_limit_gas (
    f_userinfo_id, f_limit_style, f_time_type, f_limit_type, f_start_date,
    f_third_pay, f_limit_times, f_operate_date, f_operate_people,
    f_operate_peopleid, f_state
) VALUES (
    {用户ID},
    '{限购类型}',
    '{限购周期}',
    '{支付限制}',
    '{执行时间}',
    {第三方缴费},
    '{限购次数}',
    '{操作时间}',
    '{操作员}',
    '{操作员ID}',
    '有效'
)

-- 步骤3：插入操作记录
INSERT INTO t_record (
    f_operat_type, f_describe, f_state, f_comments, f_operate_date,
    f_userinfo_id, f_user_id, f_orgid, f_orgname, f_depid, f_depname,
    f_operatorid, f_operator, f_user_name
) VALUES (
    '新增限购',
    '{操作员}对客户{用户名}进行新增限购操作',
    '有效',
    '',
    '{操作时间}',
    {用户信息ID},
    '{用户ID}',
    '{组织ID}',
    '{组织名称}',
    '{部门ID}',
    '{部门名称}',
    '{操作员ID}',
    '{操作员姓名}',
    '{用户姓名}'
)

-- 步骤4：获取新插入的限购记录ID并更新关联
SELECT TOP 1 id FROM t_limit_gas 
WHERE f_userinfo_id = {用户ID} AND f_state = '有效' 
ORDER BY id DESC

SELECT TOP 1 id FROM t_record 
WHERE f_operat_type = '新增限购' AND f_user_id = '{用户ID}' 
ORDER BY id DESC

UPDATE t_limit_gas SET f_limit_recod_id = {记录ID} 
WHERE id = {限购记录ID}
```

## 3. 关键字段说明

### t_limit_gas表关联字段
- f_limit_recod_id: 关联新增/修改限购的记录ID
- f_cancle_record_id: 关联解除限购的记录ID
- 这两个字段都对应t_record.id，用于追踪限购记录的操作历史

### f_third_pay字段处理
- 数据库存储：0（不支持）或1（支持）
- 前端可能传入：'是'/'否'、'1'/'0'、'true'/'false'
- 转换逻辑：
  - '是'/'1'/'true' → 1
  - '否'/'0'/'false' → 0
  - 默认值 → 0

### f_describe字段拼接规则
格式：{操作员}对客户{用户名}进行{操作类型}操作
示例：
- "何文强对客户测试用户_限购功能进行新增限购操作"
- "何文强对客户测试用户_限购功能进行解除限购操作"

## 4. 事务回滚机制
- 使用@Transactional(rollbackFor = Exception.class)
- 任何步骤出现异常，整个事务自动回滚
- 确保数据一致性，避免部分操作成功部分失败的情况

## 5. 错误处理
- 外键约束检查：确保f_userinfo_id在t_userinfo表中存在
- 空值处理：对可能为null的字段进行默认值处理
- SQL注入防护：使用参数化查询（当前使用字符串拼接，建议改进）
