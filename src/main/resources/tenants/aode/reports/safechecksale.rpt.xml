<report>
  <sql name="safeplan" sql="SafePlanSale" param="{startDate:startDate,endDate:endDate,f_subcompany:f_subcompany}" />
  <sql name="getfengongsi" sql="getSubcompany" param="{startDate:startDate,endDate:endDate,f_subcompany:f_subcompany}" />
  <sql name="getDepartment" sql="getDepartment" param="{startDate:startDate,endDate:endDate,f_subcompany:f_subcompany}" />
  <column number="0" width="70" startx="0" />
  <column number="1" width="70" startx="70" />
  <column number="2" width="70" startx="140" />
  <column number="3" width="70" startx="210" />
  <column number="4" width="70" startx="280" />
  <column number="5" width="70" startx="350" />
  <column number="6" width="70" startx="420" />
  <column number="7" width="70" startx="490" />
  <column number="8" width="70" startx="560" />
  <row number="0" height="30" starty="0" />
  <row number="1" height="30" starty="30" />
  <row number="2" height="30" starty="60" />
  <row number="3" height="30" starty="90" />
  <row number="4" height="30" starty="120" />
  <row number="5" height="30" starty="150" />
  <reportblock row="0" column="0" rowspan="6" columnspan="9" content="" css="" width="630" height="180">
    <headblock row="0" column="0" rowspan="2" columnspan="9" content="" css="" width="630" height="60" name="">
      <cell row="0" column="0" rowspan="2" columnspan="1" content="$分公司$" css="tdcenter report-head head-font" width="70" height="60" />
      <cell row="0" column="1" rowspan="2" columnspan="1" content="$部门$" css="tdcenter report-head head-font" width="70" height="60" />
      <cell row="0" column="2" rowspan="2" columnspan="1" content="$安检员$" css="tdcenter report-head head-font" width="70" height="60" />
      <cell row="0" column="3" rowspan="1" columnspan="4" content="$计划安检$" css="tdcenter report-head head-font" width="280" height="30" />
      <cell row="0" column="7" rowspan="1" columnspan="1" content="$集中安检$" css="tdcenter report-head head-font" width="70" height="30" />
      <cell row="0" column="8" rowspan="1" columnspan="1" content="$临时安检$" css="tdcenter report-head head-font" width="70" height="30" />
      <cell row="1" column="3" rowspan="1" columnspan="1" content="$计划数$" css="tdcenter report-head head-font" width="70" height="30" />
      <cell row="1" column="4" rowspan="1" columnspan="1" content="$计划总数$" css="tdcenter report-head head-font" width="70" height="30" />
      <cell row="1" column="5" rowspan="1" columnspan="1" content="$安检总数$" css="tdcenter report-head head-font" width="70" height="30" />
      <cell row="1" column="6" rowspan="1" columnspan="1" content="$安检率$" css="tdcenter report-head head-font" width="70" height="30" />
      <cell row="1" column="7" rowspan="1" columnspan="1" content="$安检总数$" css="tdcenter report-head head-font" width="70" height="30" />
      <cell row="1" column="8" rowspan="1" columnspan="1" content="$安检户数$" css="tdcenter report-head head-font" width="70" height="30" />
    </headblock>
    <leftblock row="2" column="0" rowspan="4" columnspan="3" content="" css="" width="210" height="120" name="">
      <leftblock row="2" column="0" rowspan="3" columnspan="3" content="getfengongsi" css="" width="210" height="90" name="left">
        <head row="2" column="0" rowspan="3" columnspan="1" content="left.f_subcompany" css="tdcenter report-left head-font" width="70" height="90" />
        <leftblock row="2" column="1" rowspan="2" columnspan="2" content="getDepartment.where(row.f_subcompany==left.f_subcompany)" css="" width="140" height="60" name="left1">
          <head row="2" column="1" rowspan="2" columnspan="1" content="left1.dename" css="tdcenter report-left head-font" width="70" height="60" />
          <leftblock row="2" column="2" rowspan="1" columnspan="1" content="safeplan.where(row.f_subcompany==left.f_subcompany).where(row.dename==left1.dename)" css="" width="70" height="30" name="left2">
            <head row="2" column="2" rowspan="1" columnspan="1" content="left2.f_checker_name" css="tdcenter report-left head-font" width="70" height="30" />
          </leftblock>
          <cell row="3" column="2" rowspan="1" columnspan="1" content="$小计$" css="tdcenter report-left head-font" width="70" height="30" />
        </leftblock>
        <cell row="4" column="1" rowspan="1" columnspan="2" content="$小计$" css="tdcenter report-left head-font" width="140" height="30" />
      </leftblock>
      <cell row="5" column="0" rowspan="1" columnspan="3" content="$总计$" css="tdcenter report-left head-font" width="210" height="30" />
    </leftblock>
    <bodyblock row="2" column="3" rowspan="4" columnspan="6" content="" css="" width="420" height="120" headexpression="" leftexpression="" headname="" leftname="">
      <bodyblock row="2" column="3" rowspan="3" columnspan="6" content="" css="" width="420" height="90" headexpression="" leftexpression="getfengongsi" headname="" leftname="left">
        <bodyblock row="2" column="3" rowspan="2" columnspan="6" content="" css="" width="420" height="60" headexpression="" leftexpression="getDepartment.where(row.f_subcompany==left.f_subcompany)" headname="" leftname="left1">
          <bodyblock row="2" column="3" rowspan="1" columnspan="6" content="" css="" width="420" height="30" headexpression="" leftexpression="safeplan.where(row.f_subcompany==left.f_subcompany).where(row.dename==left1.dename)" headname="" leftname="left2">
            <cell row="2" column="3" rowspan="1" columnspan="1" content="left2.plannum" css="tdcenter report-main main-font" width="70" height="30" />
            <cell row="2" column="4" rowspan="1" columnspan="1" content="left2.itemnum" css="tdcenter report-main main-font" width="70" height="30" />
            <cell row="2" column="5" rowspan="1" columnspan="1" content="left2.checknum" css="tdcenter report-main main-font" width="70" height="30" />
            <cell row="2" column="6" rowspan="1" columnspan="1" content="left2.checksale" css="tdcenter report-main main-font" width="70" height="30" />
            <cell row="2" column="7" rowspan="1" columnspan="1" content="left2.checknum1" css="tdcenter report-main main-font" width="70" height="30" />
            <cell row="2" column="8" rowspan="1" columnspan="1" content="left2.noplancheck" css="tdcenter report-main main-font" width="70" height="30" />
          </bodyblock>
          <cell row="3" column="3" rowspan="1" columnspan="1" content="safeplan.where(row.f_subcompany==left.f_subcompany).where(row.dname==left1.dname).sum(row.plannum)" css="tdcenter report-main main-font" width="70" height="30" />
          <cell row="3" column="4" rowspan="1" columnspan="1" content="safeplan.where(row.f_subcompany==left.f_subcompany).where(row.dname==left1.dname).sum(row.itemnum)" css="tdcenter report-main main-font" width="70" height="30" />
          <cell row="3" column="5" rowspan="1" columnspan="1" content="safeplan.where(row.f_subcompany==left.f_subcompany).where(row.dname==left1.dname).sum(row.checknum)" css="tdcenter report-main main-font" width="70" height="30" />
          <cell row="3" column="6" rowspan="1" columnspan="1" content="" css="tdcenter report-main main-font" width="70" height="30" />
          <cell row="3" column="7" rowspan="1" columnspan="1" content="safeplan.where(row.f_subcompany==left.f_subcompany).where(row.dname==left1.dname).sum(row.checknum1)" css="tdcenter report-main main-font" width="70" height="30" />
          <cell row="3" column="8" rowspan="1" columnspan="1" content="safeplan.where(row.f_subcompany==left.f_subcompany).where(row.dname==left1.dname).sum(row.noplancheck)" css="tdcenter report-main main-font" width="70" height="30" />
        </bodyblock>
        <cell row="4" column="3" rowspan="1" columnspan="1" content="safeplan.where(row.f_subcompany==left.f_subcompany).sum(row.plannum)" css="tdcenter report-main main-font" width="70" height="30" />
        <cell row="4" column="4" rowspan="1" columnspan="1" content="safeplan.where(row.f_subcompany==left.f_subcompany).sum(row.itemnum)" css="tdcenter report-main main-font" width="70" height="30" />
        <cell row="4" column="5" rowspan="1" columnspan="1" content="safeplan.where(row.f_subcompany==left.f_subcompany).sum(row.checknum)" css="tdcenter report-main main-font" width="70" height="30" />
        <cell row="4" column="6" rowspan="1" columnspan="1" content="" css="tdcenter report-main main-font" width="70" height="30" />
        <cell row="4" column="7" rowspan="1" columnspan="1" content="safeplan.where(row.f_subcompany==left.f_subcompany).sum(row.checknum1)" css="tdcenter report-main main-font" width="70" height="30" />
        <cell row="4" column="8" rowspan="1" columnspan="1" content="safeplan.where(row.f_subcompany==left.f_subcompany).sum(row.noplancheck)" css="tdcenter report-main main-font" width="70" height="30" />
      </bodyblock>
      <cell row="5" column="3" rowspan="1" columnspan="1" content="safeplan.sum(row.plannum)" css="tdcenter report-main main-font" width="70" height="30" />
      <cell row="5" column="4" rowspan="1" columnspan="1" content="safeplan.sum(row.itemnum)" css="tdcenter report-main main-font" width="70" height="30" />
      <cell row="5" column="5" rowspan="1" columnspan="1" content="safeplan.sum(row.checknum)" css="tdcenter report-main main-font" width="70" height="30" />
      <cell row="5" column="6" rowspan="1" columnspan="1" content="" css="tdcenter report-main main-font" width="70" height="30" />
      <cell row="5" column="7" rowspan="1" columnspan="1" content="safeplan.sum(row.checknum1)" css="tdcenter report-main main-font" width="70" height="30" />
      <cell row="5" column="8" rowspan="1" columnspan="1" content="safeplan.sum(row.noplancheck)" css="tdcenter report-main main-font" width="70" height="30" />
    </bodyblock>
  </reportblock>
</report>