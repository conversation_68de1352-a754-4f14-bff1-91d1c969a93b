// TODO 待兼容，说是getOrgs不用了
//定时生成年度计划
arr=getOrgs.getOrgs(),
log.info("----------------------{arr}"),
orgs=arr[0].children[0].children[0].children[0].children,
org=[],
orgs.each(
    orgschildren=row.children,
    orgschildren.length()>0:(
        orgschildren.each(
           org.put({id:row.id,name:row.name})
        )
    ),null
),
usertype=[],
usertype.put("民用"),
usertype.put("非民用"),
//生成年度计划
year=dateTools.getNowYear(),
datetime=dateTools.getNow2(),
log.info("org:{org}"),
log.info("year:{year}"),
log.info("datetimeP:{datetime}"),
log.info("usertype:{usertype}"),
org.each(
    org1=row,
    usertype.each(
        f_total_households=sql.querySQL("v3_safecheck_sql", "
            select count(1) con
            from t_user_address ua
            left join t_userinfo ui ON ui.f_userinfo_id = ua.f_userinfo_id
            left join t_userfiles uf on uf.f_userinfo_id = ui.f_userinfo_id
            where ui.f_createfile_date<'{year}-01-01 00:01:000' and ui.f_user_state='正常' and (uf.f_table_state='正常' or uf.f_table_state='停用')
            and ui.f_filialeid = {org1.id} and uf.f_user_type='{row}'
        "),
        t_year_check_plan={
            id:commonTools.getUUID(),
            f_plan_name:"{org1.name}{year}{row}年度计划",
            f_plan_year:year,
            f_plan_type:row,
            f_filialeid:org1.id,
            f_subcompany:org1.name,
            f_create_time:datetime,
            f_total_households:f_total_households[0].con
         },
         log.info("t_year_check_plan:{t_year_check_plan}"),
         sql.execSQL("v3_safecheck_sql", "
             INSERT INTO t_year_check_plan
             ([id], [f_plan_name], [f_plan_year], [f_plan_type], [f_filialeid], [f_subcompany], [f_remark], [f_create_time], [f_total_households])
             VALUES
                ('{t_year_check_plan.id}',
                '{t_year_check_plan.f_plan_name}',
                '{t_year_check_plan.f_plan_year}',
                '{t_year_check_plan.f_plan_type}',
                '{t_year_check_plan.f_filialeid}',
                '{t_year_check_plan.f_subcompany}',
                NULL,
                '{t_year_check_plan.f_create_time}',
                '{t_year_check_plan.f_total_households}'
                )
         "),
        f_total_households[0].con>0:(
            sql.execSQL("v3_safecheck_sql", "
                update t_user_address set f_year_plan_id='{t_year_check_plan.id}'
                from t_user_address ua
                join t_userinfo ui ON ui.f_userinfo_id = ua.f_userinfo_id
                join t_userfiles uf on uf.f_userinfo_id = ui.f_userinfo_id
                where ui.f_createfile_date<'{year}-01-01 00:01:000' and ui.f_user_state='正常' and (uf.f_table_state='正常' or uf.f_table_state='停用')
                and ui.f_filialeid = {org1.id} and uf.f_user_type='{row}'
                "),
            sql.execSQL("v3_safecheck_sql", "update t_user_address set f_plan_id = null where id in (
                  SELECT
                   uad.id
                  FROM
                   t_user_address uad
                  LEFT JOIN t_check_plan cp ON uad.f_plan_id = cp.id
                   where cp.f_plan_year < year(getdate()) and uad.f_plan_id is not null
                   and uad.f_orgid = {org1.id}
                 )
             ")
        ),null,
        t_year_check_plan={},
        log.info("清空年计划{t_year_check_plan}")
    )
)
