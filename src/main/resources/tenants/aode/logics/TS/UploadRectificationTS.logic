log.info("批量转隐患整改{data}"),
sqlstr = logic.run("ConditionalGeneration",data),
date_time=dateTools.getNow2(),
papers = sql.query("GetCheckPaperByDefectTS",{f_filialeids:data.f_filialeids,itemname:data.itemname,itemprocessing:data.itemprocessing,condition:sqlstr,res:data.res,f_defect_content:data.f_defect_content}),
repairs=[],
successcon=0,
errcon=0,
nohidden=0,
repeatCount=0,
serviceacitivity=[],
serviceacitivity.put(data.activity),
options=[],
options.put({data: "已处理"}),
options.put({data: "未处理"}),
log.info("=================={papers.length()}"),
papers.length()>0:(
    papers.each(
        //筛除paper中f_repaired 已经派发过的单子不在重复派发
        row.f_repaired=="未修":(
            paper=row,
            f_repairitemsdata=jsonTools.getArray(),
            failure=jsonTools.getArray(),
            defectlist=jsonTools.getArray(),
            //查询当前单子的所有隐患需要转到工单中的隐患  and (di.f_defect_level LIKE '一级%' or di.f_defect_level LIKE '二级%')
            // t_devices_items 的字段 f_is_repaired： 是否已经转维修处理过了 【已转维修、需转维修、不转维修，空字符串】。 不转维修：包含用户自行整改过的和现场处理过的
            defect=sql.querySQL("v3_safecheck_sql", "
                select pd.f_device_type,di.id,di.type,di.f_item_name,dil.f_item_value from t_paper_devices_ts pd
                    left join t_devices_items_ts di on pd.id=di.f_device_id
                    left join t_devices_items_lists_ts dil on dil.f_item_id=di.id
                    where pd.f_paper_id='{paper.id}'
                        and di.f_is_defect = 'true'
                        and di.f_item_name not like '%异常差值%'
                        and di.f_is_repaired = '需转维修'
            "),
            //遍历隐患-找出所有需要隐患整改的隐患，统一放入隐患数组
            defect.each(
                    rowindex=0,
                    existIndex=-1,
                    defectRow=row,
                    log.info("defectRow:{defectRow}"),
                    defectlist.put(defectRow.id),
                    f_repairitemsdata.each(
                        defectRow.f_device_type==row.f_type:(
                            existIndex=rowindex
                        ),null,
                        rowindex=rowindex+1
                    ),
                    existIndex==-1:(
                        details=jsonTools.getArray(),
                        newitem={},
                        log.info("existIndex:{existIndex}--{newitem}--{details}"),
                        details.put({
                            f_project: "{defectRow.f_item_name}:{defectRow.f_item_value}",
                            type: "selector",
                            options: options,
                            f_defect_id:defectRow.id
                        }),
                        log.info("details:--{details}"),
                        newitem={
                             f_type: defectRow.f_device_type,
                             details: details
                        },
                        f_repairitemsdata.put(newitem)
                    ),(
                    log.info("existIndex:{existIndex}"),
                        f_repairitemsdata[existIndex].details.put({
                          f_project: "{defectRow.f_item_name}:{defectRow.f_item_value}",
                          type: "selector",
                          options: options,
                          f_defect_id:defectRow.id
                        })
                    ),
                    failurecase=jsonTools.getArray(),
                    failurecase.put(defectRow.f_item_value),
                    failure.put({f_failure_type:defectRow.f_item_name,failurecase:failurecase})

            ),
            //上一步完成后 隐患数组中有符合条件的隐患 就进行数据组装然后调用工单生成接口
            defectlist.length()>0:(
                model={
                aState: "",
                f_address: paper.f_address,
                f_area: "",
                f_attendant: data.f_attendant,
                f_building: paper.f_building,
                f_contact_phone: paper.f_user_phone,
                f_filiale_id: paper.f_filialeid,
                f_floor: paper.f_floor,
                f_meetunit: data.activity.f_meetunit,
                f_orgstr: paper.f_filialeid,
                f_orgname: paper.f_orgname,
                f_outlets: data.deps,
                f_phone: "",
                f_checkpaper_number:paper.f_checkpaper_number,
                f_reciever: "",
                f_remarks: data.f_remarks,
                f_repair_date: dateTools.getNow2(),
                f_repairitems: {data:f_repairitemsdata},
                f_repairtype: data.f_repairtype,
                f_residential_area: paper.f_residential_area,
                f_room: paper.f_room,
                f_service_id: "",
                f_source: "安检",
                f_street: paper.f_street,
                f_unit: paper.f_unit,
                f_unit_name: "",
                f_yuyue_date:paper.f_onsite_time,
                f_user_name: paper.f_user_name,
                f_user_type: paper.f_check_type,
                f_userinfo_code: paper.f_userinfo_code,
                f_userinfo_id: paper.f_userinfoid,
                failure: failure,
                f_operator_name:data.f_attendant,
                serviceacitivity: serviceacitivity,
                f_small_area:paper.f_small_area,
                f_street:paper.f_street
            },
            dataStr={
                toRepair:data.toRepair,
                model:model,
                loginUser:data.loginUser,
                user:{f_userinfo_id: paper.f_userinfo_id},
                callObj:{}
            },
            log.info("---data{dataStr}---------------{paper.f_userinfo_id}"),
            //slash = "/",
            //result=jsonTools.convertToJson(httputil.post("http:/{slash}39.101.182.117:8909/api/cc/workorder.ashx?sign=FC96F77A-0CE4-44D1-AC2D-8C678E337AB9&method=postpreorders", repairs)),
            result = jsonTools.convertToJson(restTools.post("/rs/logic/callerSend",dataStr)),
            log.info("-------------调用维修接口成功---{result}----{data}"),
            //成功后修改本条安检单状态
            result.has("f_service_id"):(
                d=",",
                s=jsoncompare.jsonArrayFormat(defectlist,d),
                logic.run("updateDefectStateZG",{itemsId:"({s})",paperid:paper.id,f_rectification_man:data.repairman,serviceid:result.f_service_id}),
                successcon=successcon+1
            ),(errcon=errcon+1)
            ),(
                nohidden=nohidden+1
            )

        ),(
            repeatCount=repeatCount+1
        )

    )


),null,
{
    code:200,
    successcon:successcon,
    errcon:errcon,
    repeatCount:repeatCount,
    nohidden:nohidden
}


