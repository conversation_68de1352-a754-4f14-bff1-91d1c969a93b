sql.execSQL("v3_safecheck_sql", "truncate table t_gasCount_ts"),
sql.execSQL("v3_safecheck_sql", "insert  into t_gasCount_ts
         SELECT
             benci.*,
             --tuf.f_meter_classify,
             shangci.used as lastused,
             --benci.f_filialeid,
             shangci.remain as lastremain,
             shangci.f_offsite_time as lasttime,
             shangci.hisgas as lasthisgas,
             shangci.totalgas as lasttotalgas,
             CONVERT(NUMERIC(19,2),convert(float,benci.totalgas)-convert(float,shangci.totalgas)) as amounttotalgas,
             CONVERT(NUMERIC(19,2),convert(float,benci.used)-convert(float,shangci.used)) as amountusedgas,
             DATEDIFF(day,shangci.f_offsite_time, benci.f_offsite_time) as amountday,
             CASE  when DATEDIFF(day,shangci.f_offsite_time, benci.f_offsite_time)=0 THEN 0 else CONVERT(NUMERIC(19,2),(convert(float,benci.used)-convert(float,shangci.used))/DATEDIFF(day,shangci.f_offsite_time, benci.f_offsite_time) ) end as avggas,
             '' as changetablegas,
             '' as beforetablegas,
			 GETDATE() as insertdate,
			 '' as aa,
			 '' as amounttotalgas2,
			 '' as amountusedgas2,
			 '' as startnum,
			 '' as endnum,
			 '' as reshuiqi,
			 '' as bigualu,
			 '' as lastgasdate

         FROM
             (
                 SELECT
                     tt.*,
                     tuf.f_meter_classify,
                     tuf.f_table_state,
                     ranqi.*
                 FROM
                     (
                         SELECT
                             cpid,
                             f_checker_name,
                             f_userinfo_code,
                             f_address,
                             f_user_name,
                             f_userinfoid,
                             f_offsite_time,
                             f_upload_date,
                             f_check_item_id,
                             ROW_NUMBER ( ) OVER ( partition BY f_userinfoid ORDER BY f_offsite_time DESC ) AS nn
                         FROM
                             (
                                 SELECT
                                     *
                                 FROM
                                     (
                                         SELECT
                                             id cpid,
                                             f_checker_name,
                                             f_userinfo_code,
                                             f_address,
                                             f_user_name,
                                             f_userinfoid,
                                             f_offsite_time,
                                             f_upload_date,
                                             f_check_item_id,
                                             f_filialeid,
                                             ROW_NUMBER ( ) OVER ( partition BY f_userinfoid, f_check_item_id ORDER BY f_offsite_time DESC ) AS rn
                                         FROM
                                             t_check_paper_ts with ( nolock)
                                         WHERE
                                             f_entry_status = '入户'
                                           and  1=1
                                     ) AS u
                                 WHERE
                                         u.rn= 1 --AND f_user_name = 'A01122001087660测试'

                             ) dd
                     ) AS tt
                         LEFT JOIN t_userfiles_ts tuf with ( nolock) on tt.f_userinfoid=tuf.f_userinfo_id
                             LEFT JOIN (
                             SELECT
                             f_paper_id,
                             MAX ( a.已用气量 ) AS used,
                             MAX ( a.剩余气量 ) AS remain,
                             MAX ( a.户累购气量 ) AS hisgas,
                             MAX ( a.累购气量 ) AS totalgas
                             FROM
                             (
                                 SELECT
                                     f_paper_id,
                                     tdi.f_item_name,
                                     tdil.f_item_value
                                 FROM
                                     t_paper_devices_ts  tpd with ( nolock)
                                     LEFT JOIN t_devices_items_ts tdi with ( nolock) ON tpd.id= tdi.f_device_id
                                     LEFT JOIN t_devices_items_lists_ts tdil with ( nolock) ON tdi.id= tdil.f_item_id
                                 WHERE
                                 f_device_type = '燃气表'
                             ) tt PIVOT ( MAX ( tt.f_item_value ) FOR tt.f_item_name IN ( 已用气量,剩余气量,户累购气量,累购气量 ) ) a
                         GROUP BY
                             a.f_paper_id
                     ) ranqi ON tt.cpid= ranqi.f_paper_id
                 WHERE
                    tt.nn= 1
             ) benci
                 LEFT JOIN (
                 SELECT
                     *
                 FROM
                     (
                         SELECT
                             cpid,
                             f_checker_name,
                             f_user_name,
                             f_userinfoid,
                             f_offsite_time,
                             f_check_item_id,
                             ROW_NUMBER ( ) OVER ( partition BY f_userinfoid ORDER BY f_offsite_time DESC ) AS nn
                         FROM
                             (
                                 SELECT
                                     *
                                 FROM
                                     (
                                         SELECT
                                             id cpid,
                                             f_checker_name,
                                             f_user_name,
                                             f_userinfoid,
                                             f_offsite_time,
                                             f_check_item_id,
                                             f_filialeid,
                                             ROW_NUMBER ( ) OVER ( partition BY f_userinfoid, f_check_item_id ORDER BY f_offsite_time DESC ) AS rn
                                         FROM
                                             t_check_paper_ts with ( nolock)
                                         WHERE
                                             f_entry_status = '入户'
                                           and  1=1
                                     ) AS u
                                 WHERE
                                         u.rn= 1 --AND f_user_name = 'A01122001087660测试'

                             ) dd
                     ) AS tt
                         LEFT JOIN (
                         SELECT
                             f_paper_id,
                             MAX ( a.已用气量 ) AS used,
                             MAX ( a.剩余气量 ) AS remain,
                             MAX ( a.户累购气量 ) AS hisgas,
                             MAX ( a.累购气量 ) AS totalgas
                         FROM
                             (
                                 SELECT
                                     f_paper_id,
                                     tdi.f_item_name,
                                     tdil.f_item_value
                                 FROM
                                     t_paper_devices_ts tpd with ( nolock)
                                         LEFT JOIN t_devices_items_ts tdi with ( nolock) ON tpd.id= tdi.f_device_id
                                     LEFT JOIN t_devices_items_lists_ts tdil with ( nolock) ON tdi.id= tdil.f_item_id
                                 WHERE
                                     f_device_type = '燃气表'
                             ) tt PIVOT ( MAX ( tt.f_item_value ) FOR tt.f_item_name IN ( 已用气量,剩余气量,户累购气量,累购气量 ) ) a
                         GROUP BY
                             a.f_paper_id
                     ) ranqi ON tt.cpid= ranqi.f_paper_id
                 WHERE
                         tt.nn= 2
             ) shangci ON benci.f_userinfoid= shangci.f_userinfoid
         where  benci.f_table_state='正常' and benci.f_meter_classify!='物联网表'
"),
sql.execSQL("v3_safecheck_sql", "
INSERT INTO t_gasCount_ts SELECT
newid( ),
'未安检',
ui.f_userinfo_code,
ua.f_address,
ui.f_user_name,
ui.f_userinfo_id,
CASE

		WHEN uf.f_open_date IS NULL THEN
		CONVERT ( VARCHAR ( 100 ), ui.f_open_date, 20 ) ELSE CONVERT ( VARCHAR ( 100 ), uf.f_open_date, 20 )
	END AS f_offsite_time,
CASE

		WHEN uf.f_open_date IS NULL THEN
		CONVERT ( VARCHAR ( 100 ), ui.f_open_date, 20 ) ELSE CONVERT ( VARCHAR ( 100 ), uf.f_open_date, 20 )
	END AS f_upload_date,
	NULL,
	1,
	NULL,
	'正常',
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
CASE

		WHEN uf.f_open_date IS NULL THEN
		CONVERT ( VARCHAR ( 100 ), ui.f_open_date, 20 ) ELSE CONVERT ( VARCHAR ( 100 ), uf.f_open_date, 20 )
	END AS lasttime,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	0,
	0,
	GETDATE( ),
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
NULL
FROM
	t_userinfo_ts ui WITH ( nolock )
	INNER JOIN t_userfiles_ts uf WITH ( nolock ) ON ui.f_userinfo_id= uf.f_userinfo_id
	AND uf.f_table_state= '正常'
	LEFT JOIN t_user_address_ts ua WITH ( nolock ) ON ui.f_userinfo_id= ua.f_userinfo_id
	LEFT JOIN t_gasCount_ts gc WITH ( nolock ) ON ui.f_userinfo_id  = gc.f_userinfoid
WHERE
	1 = 1

	AND gc.cpid IS NULL

"),
sql.execSQL("v3_safecheck_sql", "UPDATE t_gasCount_ts
SET lasttime = uf.f_open_date,
lastused = '0',
lastremain = uf.f_pregas,
amountday = DATEDIFF( DAY, uf.f_open_date, gas.f_offsite_time )
FROM
	t_gasCount_ts gas,
	(
	SELECT
	CASE

		WHEN
			tsg.f_pregas IS NULL THEN
				'0' ELSE tsg.f_pregas
			END f_pregas,
	tgc.f_offsite_time,
	tgc.f_userinfoid,
CASE

	WHEN tuf.f_open_date IS NULL THEN
	CONVERT ( VARCHAR ( 100 ), tui.f_open_date, 20 ) ELSE CONVERT ( VARCHAR ( 100 ), tuf.f_open_date, 20 )
	END AS f_open_date
FROM
	t_gasCount_ts tgc WITH ( nolock )
	LEFT JOIN t_userfiles_ts tuf WITH ( nolock ) ON tgc.f_userinfoid= tuf.f_userinfo_id
	LEFT JOIN t_userinfo_ts tui WITH ( nolock ) ON tuf.f_userinfo_id= tui.f_userinfo_id
	LEFT JOIN (
	SELECT
		f_pregas,
		f_userinfo_id
	FROM
		t_sellinggas sg WITH ( nolock )
	WHERE
		sg.id = ( SELECT MIN ( id ) FROM t_sellinggas WITH ( nolock ) WHERE f_type = '发卡售气' and f_state='有效' and f_userinfo_id = sg.f_userinfo_id )
	) tsg ON tui.f_userinfo_id= tsg.f_userinfo_id
WHERE
	tuf.f_table_state= '正常'
	AND lasttime IS NULL
	) uf
WHERE
	gas.f_userinfoid= uf.f_userinfoid"),
sql.execSQL("v3_safecheck_sql", "update t_gasCount_ts  SET beforetablegas=dd.sumgas from  t_gasCount_ts gas,(

         select cpid,CONVERT(VARCHAR(50),sum(CONVERT(FLOAT,f_pregas))) as sumgas from (SELECT
         	paper.cpid,
         	paper.f_offsite_time,
         	tctime.*
         FROM
         		t_gasCount_ts paper
         	JOIN (
         	SELECT
         		*
         	FROM
         		 t_sellinggas WITH ( nolock )
         	WHERE
         		f_type = '卡表收费' and f_state='有效'
         	) tctime ON paper.f_userinfoid= tctime.f_userinfo_id

         	where lasttime>f_operate_date) tt
         	GROUP BY tt.cpid) dd where gas.cpid=dd.cpid"),
sql.execSQL("v3_safecheck_sql", "update t_gasCount_ts  SET changetablegas=dd.sumgas from  t_gasCount_ts gas,(select cpid,CONVERT(VARCHAR(50),sum(CONVERT(FLOAT,f_pregas))) as sumgas from (SELECT
          	paper.cpid,
          	paper.f_offsite_time,
          	tctime.*
          FROM
          		t_gasCount_ts paper
          	JOIN (
          	SELECT
          		*
          	FROM
          		 t_sellinggas WITH ( nolock )
          	WHERE
          		f_type = '卡表收费' and f_state='有效'
          	) tctime ON paper.f_userinfoid= tctime.f_userinfo_id

          	where f_offsite_time>f_operate_date) tt
          	GROUP BY tt.cpid) dd where gas.cpid=dd.cpid"),
sql.execSQL("v3_safecheck_sql", "update t_gasCount_ts  SET startnum=dd.startnum,endnum=dd.endnum from  t_gasCount_ts gas,(

         select
				 cpid,
				 CONVERT(VARCHAR(50),sum(CONVERT(FLOAT,f_initial_base_new))) as startnum,
				 CONVERT(VARCHAR(50),sum(CONVERT(FLOAT,f_using_base_old))) as endnum

				 from (SELECT
         	paper.cpid,
         	paper.f_offsite_time,
         	tctime.*
         FROM
         		t_gasCount_ts paper WITH ( nolock )
         	JOIN (
         	SELECT
         		*
         	FROM
         		 t_changetables WITH ( nolock )
         	WHERE
         		 f_state='有效' and f_type='换新表'
         	) tctime ON paper.f_userinfoid= tctime.f_userinfo_id

         	where lasttime<f_operate_date and f_offsite_time>f_operate_date) tt
         	GROUP BY tt.cpid
					) dd where gas.cpid=dd.cpid"),
sql.execSQL("v3_safecheck_sql", "update t_gasCount_ts set amountusedgas2=CONVERT (
			NUMERIC ( 19, 2 ),
			CONVERT ( FLOAT, endnum ) - CONVERT ( FLOAT, lastused ) + CONVERT ( FLOAT, used ) - CONVERT ( FLOAT, startnum )
		),amounttotalgas2=CONVERT (
			NUMERIC ( 19, 2 ),
			( CONVERT ( FLOAT, ISNULL( changetablegas, 0 ) ) - CONVERT ( FLOAT, ISNULL( beforetablegas, 0 ) ) )
		),aa=CASE

			WHEN amountday = 0 THEN
			0 ELSE CONVERT (
				NUMERIC ( 19, 2 ),
				(
					CONVERT ( FLOAT, endnum ) - CONVERT ( FLOAT, lastused ) + CONVERT ( FLOAT, used ) - CONVERT ( FLOAT, startnum )
				) / amountday
			)
		END"),
sql.execSQL("v3_safecheck_sql", "
    update t_gasCount_ts set reshuiqi=tt.reshuiqi,bigualu=tt.bigualu from (
    SELECT
            f_paper_id,
            MAX ( a.是否为壁挂炉用户 ) AS bigualu,
            MAX ( a.是否为热水器用户 ) AS reshuiqi
        FROM
            (
            SELECT
                f_paper_id,
                tdi.f_item_name,
                tdil.f_item_value
            FROM
                t_paper_devices_ts tpd WITH ( nolock )
                LEFT JOIN t_devices_items_ts tdi WITH ( nolock ) ON tpd.id= tdi.f_device_id
                LEFT JOIN t_devices_items_lists_ts tdil WITH ( nolock ) ON tdi.id= tdil.f_item_id
            WHERE
                f_device_type = '安检详情'
            ) tt PIVOT ( MAX ( tt.f_item_value ) FOR tt.f_item_name IN ( 是否为壁挂炉用户,是否为热水器用户 ) ) a
        GROUP BY
            a.f_paper_id
    ) tt where cpid=tt.f_paper_id"
),
sql.execSQL("v3_safecheck_sql", "
    UPDATE t_check_paper_ts
    SET f_reshuiqi = tt.reshuiqi,
    f_bigualu = tt.bigualu,
    f_leigou=tt.leigou,
    f_shengyu=tt.shengyu,
    f_yiyong=tt.yiyong,
    f_subcompany='是'
    FROM
        (
        SELECT
            f_paper_id,
            MAX ( a.是否为壁挂炉用户 ) AS bigualu,
            MAX ( a.已用气量 ) AS yiyong,
            MAX ( a.剩余气量 ) AS shengyu,
            MAX ( a.累购气量 ) AS leigou,
            MAX ( a.是否为热水器用户 ) AS reshuiqi
        FROM
            (
            SELECT
                f_paper_id,
                tdi.f_item_name,
                tdil.f_item_value
            FROM
                t_paper_devices_ts tpd WITH ( nolock )
                LEFT JOIN t_devices_items_ts tdi WITH ( nolock ) ON tpd.id= tdi.f_device_id
                LEFT JOIN t_devices_items_lists_ts tdil WITH ( nolock ) ON tdi.id= tdil.f_item_id
            WHERE
                f_device_type = '安检详情' or f_device_type = '燃气表'
            ) tt PIVOT ( MAX ( tt.f_item_value ) FOR tt.f_item_name IN ( 已用气量,剩余气量,累购气量,是否为壁挂炉用户,是否为热水器用户 ) ) a
        GROUP BY
            a.f_paper_id
        ) tt
    WHERE
        id = tt.f_paper_id and f_subcompany is null"
),
sql.execSQL("v3_safecheck_sql", "
    UPDATE t_gasCount_ts
     SET lastgasdate = CONVERT ( VARCHAR ( 100 ), tt.f_operate_date, 21 )
     FROM
        (
        SELECT
            *
        FROM
            (
            SELECT
                f_operate_date,
                f_userinfo_id,
                ROW_NUMBER ( ) OVER ( partition BY f_userinfo_id ORDER BY f_operate_date DESC ) AS rn
            FROM
                t_sellinggas WITH ( nolock )
            WHERE
                f_type = '卡表收费'
                AND f_state = '有效'
                AND 1 = 1
            ) AS u
        WHERE
            u.rn= 1
        ) tt
     WHERE
        f_userinfoid = tt.f_userinfo_id"
)


