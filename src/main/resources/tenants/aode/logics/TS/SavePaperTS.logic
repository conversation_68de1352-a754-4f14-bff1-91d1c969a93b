
log.info("--------------参数：{data}---------------"),
sql.action({sql: "delete from t_devices_items_ts where f_device_id in (select id from t_paper_devices_ts where f_paper_id in ( select id from t_check_paper_ts where f_check_item_id = '{data.f_check_item_id}' and f_checker_id= '{data.f_checker_id}')) ", cmd: "cmd"}),
sql.action({sql: "delete from t_paper_devices_ts where f_paper_id in (select id from t_check_paper_ts where f_check_item_id = '{data.f_check_item_id}' and f_checker_id= '{data.f_checker_id}')", cmd: "cmd"}),
sql.action({sql: "delete from t_check_paper_ts where f_check_item_id = '{data.f_check_item_id}' and f_checker_id= '{data.f_checker_id}'", cmd: "cmd"}),

log.info("-----------安检单：{data.row}---------------------"),

data.row.f_state = "已检",
EmptyJSONIds.empty(data.row),
entity.partialSave("t_check_paper_ts", data.row),

 sql.action({sql: "update t_check_plan_item_ts set f_state='已检',f_upload_state='未传',f_complete = '{data.row.f_complete}',f_last_check_state='{data.row.f_entry_status}',
 f_prearranged_date = null  where id = '{data.f_check_item_id}'", cmd: "cmd"}),

{code:200}
