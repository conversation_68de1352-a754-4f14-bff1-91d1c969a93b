//新增安检计划项 并添加活动
//data:{
//      {                                                                                   }
//      f_plan_id:'',
//      f_operator:'',
//       f_filialeid:'',
//      f_create_time:'',
//      condition:'sql条件', 查询条件
//      switchCheckAll:true/false是否有选择功能
//      checkAll:true/false, 是否选中 ‘全选按钮’
//      param[...], 选中的字段的值
//      columnName 需要做查询的字段名
//}
//
log.info("---------data:{data}"),
data.f_remark != null :(
	f_remark = data.f_remark
),(
	f_remark = ""
),

sqlstr = logic.run("ConditionalGeneration",data),
data.f_create_time=dateTools.getNow2(),
newid=commonTools.getUUID(),
log.info("当前时间{data.f_create_time}"),
// 查询当前计划的计划数
plancon = sql.querySQL("查询当前计划的计划数", "
    select count(id) con from t_check_plan_item_ts  WITH(nolock) where f_plan_id =  '{data.f_plan_id}'
"),
// 查询添加到计划的用户数
arrs1=sql.querySQL("v3_safecheck_sql", "
    select count(1) con
    from t_user_address_ts ua
    join t_userinfo_ts ui ON ui.f_userinfo_id = ua.f_userinfo_id
    left join t_userfiles_ts uf on uf.f_userinfo_id = ui.f_userinfo_id
    where {sqlstr} and (uf.f_table_state='正常' or uf.f_table_state='停用')
"),
// 单条计划数必须小于5000条
sqlCount = 0,
arrs1.length() > 0: (
    sqlCount = arrs1[0].con
),null,
// 当前计划的原计划数
thisPlanCount = 0,
plancon.length() > 0: (
    thisPlanCount = plancon[0].con
),null,

commonTools.add(sqlCount, thisPlanCount) < 5000: (
    service_id = "",
    data.has("service_id"):(
        service_id = data.service_id
    ),null,
    // 新增item数据
    data.f_user_phone != null && data.f_address != null :(
    sql.execSQL("v3_safecheck_sql", "
                INSERT INTO t_check_plan_item_ts (
                                    id,
                                    f_userinfo_code,
                                    f_userinfoid,
                                    f_idnumber,
                                    f_area,
                                    f_slice_area,
                                    f_street,
                                    f_residential_area,
                                    f_building,
                                    f_unit,
                                    f_floor,
                                    f_room,
                                    f_address,
                                    f_state,
                                    f_upload_state,
                                    f_plan_id,
                                    f_user_name,
                                    f_user_phone,
                                    f_user_type,
                                    f_approved,
                                    f_no_checkplan,
                                    f_last_check_state,
                                    f_last_check_date,
                                    f_repair_approved,
                                    f_filialeid,
                                    f_create_time,
                                    version,
                                    f_last_check_result,
                                    f_comments,
                                    f_addressid,
                                    f_safecheck_type,
                                    f_year_plan_id,
                                    f_remark,
                                    f_open_date,
                                    f_check_version,
                                    f_insurance_gas_date,
                                    service_id,
                                    f_user_source
                                    ) SELECT
                                    newId(),
                                    ui.f_userinfo_code,
                                    ui.f_userinfo_id,
                                    ui.f_idnumber,
                                    ua.f_area,
                                    ua.f_slice_area,
                                    ua.f_street,
                                    ua.f_residential_area,
                                    ua.f_building,
                                    ua.f_unit,
                                    ua.f_floor,
                                    ua.f_room,
                                    '{data.f_address}',
                                    '未检',
                                    '未传',
                                    '{data.f_plan_id}',
                                    ui.f_user_name,
                                   '{data.f_user_phone}',
                                    uf.f_user_type,
                                    '未审核',
                                    '有计划安检',
                                    ua.f_last_check_state,
                                    ua.f_last_check_date,
                                    '未审核',
                                    '{data.f_filialeid}',
                                    '{data.f_create_time}',
                                    ui.version,
                                    ua.f_last_check_result,
                                    ui.f_comments,
                                    ua.id,
                                    '{data.f_safecheck_type}',
                                    ua.f_year_plan_id,
                                     '{f_remark}',
                                     CONVERT(VARCHAR(32),ui.f_open_date,121),
                                     0,
                                     CONVERT(varchar(100),va.f_start_date,23),
                                     '{service_id}',
                                     't_userinfo_ts'
                                    FROM
                                        t_user_address_ts ua WITH(nolock)
                                       left join t_userinfo_ts ui WITH(nolock) ON ui.f_userinfo_id = ua.f_userinfo_id
                                       left join t_value_add va WITH(nolock) on ui.f_userinfo_id = va.f_userinfo_id and f_value_name='燃气险'
                                       left join t_userfiles_ts uf WITH(nolock) on uf.f_userinfo_id = ui.f_userinfo_id
                                    WHERE {sqlstr} and (uf.f_table_state='正常' or uf.f_table_state='停用')
        ")
    ),(
    sql.execSQL("v3_safecheck_sql", "
            INSERT INTO t_check_plan_item_ts (
                                id,
                                f_userinfo_code,
                                f_userinfoid,
                                f_idnumber,
                                f_area,
                                f_slice_area,
                                f_street,
                                f_residential_area,
                                f_building,
                                f_unit,
                                f_floor,
                                f_room,
                                f_address,
                                f_state,
                                f_upload_state,
                                f_plan_id,
                                f_user_name,
                                f_user_phone,
                                f_user_type,
                                f_approved,
                                f_no_checkplan,
                                f_last_check_state,
                                f_last_check_date,
                                f_repair_approved,
                                f_filialeid,
                                f_create_time,
                                version,
                                f_last_check_result,
                                f_comments,
                                f_addressid,
                                f_safecheck_type,
                                f_year_plan_id,
                                f_remark,
                                f_open_date,
                                f_check_version,
                                f_insurance_gas_date,
                                service_id,
                                f_user_source
                                ) SELECT
                                newId(),
                                ui.f_userinfo_code,
                                ui.f_userinfo_id,
                                ui.f_idnumber,
                                ua.f_area,
                                ua.f_slice_area,
                                ua.f_street,
                                ua.f_residential_area,
                                ua.f_building,
                                ua.f_unit,
                                ua.f_floor,
                                ua.f_room,
                                ua.f_address,
                                '未检',
                                '未传',
                                '{data.f_plan_id}',
                                ui.f_user_name,
                                ui.f_user_phone,
                                uf.f_user_type,
                                '未审核',
                                '有计划安检',
                                ua.f_last_check_state,
                                ua.f_last_check_date,
                                '未审核',
                                '{data.f_filialeid}',
                                '{data.f_create_time}',
                                ui.version,
                                ua.f_last_check_result,
                                ui.f_comments,
                                ua.id,
                                '{data.f_safecheck_type}',
                                ua.f_year_plan_id,
                                 '{f_remark}',
                                 CONVERT(VARCHAR(32),ui.f_open_date,121),
                                 0,
                                 CONVERT(varchar(100),va.f_start_date,23),
                                 '{service_id}',
                                  't_userinfo_ts'
                                FROM
                                    t_user_address_ts ua WITH(nolock)
                                   left join t_userinfo_ts ui WITH(nolock) ON ui.f_userinfo_id = ua.f_userinfo_id
                                   left join t_value_add va WITH(nolock) on ui.f_userinfo_id = va.f_userinfo_id and f_value_name='燃气险'
                                   left join t_userfiles_ts uf WITH(nolock) on uf.f_userinfo_id = ui.f_userinfo_id
                                WHERE {sqlstr}  and (uf.f_table_state='正常' or uf.f_table_state='停用')
    ")
    ),
     //生成对应meter表信息
     sql.execSQL("v3_safecheck_sql", "insert into t_plan_item_meter_ts (id,f_weizhi,f_maker,f_diaphgram_size,f_aliasname,f_meter_type,f_prior_reading,f_meter_no,f_flow_direction,f_metertitles,f_metergasnums,f_plan_item_id)
              select
                newId(),
                f_position AS f_weizhi,
                gb.f_manufacturers AS f_maker,
                gm.f_meter_style AS f_diaphgram_size,
                gb.f_meter_brand AS f_aliasname,
                gm.f_type AS f_meter_type,
                f_meter_base AS f_prior_reading,
                uf.f_meternumber AS f_meter_no,
                uf.f_aroundmeter AS f_flow_direction,
                uf.f_metertitles As f_metertitles,
                uf.f_meter_base As  f_metergasnums,
                 cpi.id   As  f_plan_item_id
              FROM
                t_user_address_ts ua WITH(nolock)
                left join t_userinfo_ts ui WITH(nolock) ON ui.f_userinfo_id = ua.f_userinfo_id
                left join t_userfiles_ts uf WITH(nolock) on ua.f_userinfo_id= uf.f_userinfo_id
                left join (
                    select max(f_create_time) maxtime,f_userinfoid from t_check_plan_item_ts group by f_userinfoid
                  ) cpii ON ua.f_userinfo_id = cpii.f_userinfoid
                left join t_check_plan_item_ts cpi on  cpii.maxtime = cpi.f_create_time and cpii.f_userinfoid = cpi.f_userinfoid
                LEFT JOIN t_gasbrand gb WITH(nolock) ON uf.f_gasbrand_id = gb.id
                LEFT JOIN  t_gasmodel gm WITH(nolock) on uf.f_gasmodel_id = gm.id
              WHERE
                {sqlstr} and (uf.f_table_state='正常' or uf.f_table_state='停用')
     "),

    // 添加派单动作，注意继承关系
    sql.execSQL("v3_safecheck_sql", "
            INSERT INTO t_check_acitivity(serviceid,f_service_acitivity_type,f_batch_number)
                SELECT
                    it.id,
                    '派单',
                    '{newid}'
                 from t_check_plan_item_ts it WITH(nolock)
                 join t_user_address_ts ua WITH(nolock) on it.f_addressid=ua.id
                 join t_userinfo_ts ui WITH(nolock) ON ui.f_userinfo_id =  it.f_userinfoid
                 join t_userfiles_ts uf WITH(nolock) on uf.f_userinfo_id = it.f_userinfoid
                 WHERE it.f_plan_id='{data.f_plan_id}' and {sqlstr}  and (uf.f_table_state='正常' or uf.f_table_state='停用')
    "),
    sql.execSQL("v3_safecheck_sql", "
            INSERT INTO t_check_send (id,serviceid,f_service_acitivity_type,f_date,f_operator,f_meetunit,f_send_planid,f_send_planname,f_batch_number)
             SELECT
                sa.id,
                sa.serviceid,
                sa.f_service_acitivity_type,
                '{data.f_create_time}',
                '{data.f_operator}',
                cp.f_checker,
                cp.id,
                cp.f_plan_name,
                '{newid}'
              from t_check_acitivity sa WITH(nolock)
              join t_check_plan_item it WITH(nolock) on sa.serviceid=it.id
              join t_check_plan cp WITH(nolock) on it.f_plan_id=cp.id
              where sa.f_batch_number='{newid}'
    "),
    {code:200,resultno:arrs1}
),(
    {code:501,resultno:arrs1,msg: "派发失败，每个计划下最多5000户，您本次添加了{sqlCount}户，请分为多个计划"}
)



