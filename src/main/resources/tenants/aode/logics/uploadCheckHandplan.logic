// 1.进入上传后，先上传本次抄表信息中的所有图片
// 2.当所有图片上传成功后，进入判断是否是非民用，非民用需要判断是否换流量计，民用正常上传保存即可
// 3.如果换流量计了，需要先上传新流量计的所有照片，当新流量计的所有照片上传成功后再上传旧流量计和新流量计的所有信息
log.info("进入安检抄表数据保存逻辑:{data}"),
f_is_thisimage = false, // 本次照片上传是否成功
f_is_newimage = false, // 换流量计后照片是否成功
result = {code: 500,msg: "服务器内部错误"},
try {
data.f_meter_paths != null && data.f_meter_paths.length() != 0 : (
       f_meter_path_names = "", //文件名  多个文件名以逗号分割
       f_meter_paths = "", //文件地址 多个文件以逗号分割
       data.f_meter_paths.each(
           // 本地地址中解析文件名
           imgfileName = (row.f_meter_path).substring((row.f_meter_path).lastIndexOf("/") + 1, (row.f_meter_path).length()),
           url = "{context.cc_base_url}/api/af-task/file/saveFileForm?module=telephone&filename={imgfileName}",
           log.info("打印url:{url}_{imgfileName}"),
           file = row.f_meter_path.substring(8),
           imgid = UploadPlugin.upload_form_new(url, file,{}),
           // 上传是否成功
           imgid == null: (
                   f_is_thisimage = false,
                   log.info("name:upload,msg: 上传图片失败"),
                   throw {status: 500, msg: "上传流量计照片失败"}
               ), (
                   f_is_thisimage = true,
                   f_meter_path_names = "{f_meter_path_names},{imgfileName}",
                   f_meter_paths = "{f_meter_paths},{row.f_meter_path}",
                   log.info("name:upload,msg: 原流量计控制器图片上传成功")
               )
       ),
       //上传签名图片
       log.info("name:upload,msg: ------开始上传签名照片----"),
       data.has("signfileName") && data.signfileName != null && data.signfileName != "": (
           url = "{context.cc_base_url}/api/af-task/file/saveFileForm?module=telephone&filename={data.signfileName}",
           log.info("name:upload,msg: -----------url is {url}--- {data.signfileName}--------"),
           file = data.f_sign_path.substring(8),
           log.info("name:upload, msg:------------file is {file}-----------"),
           imgid = UploadPlugin.upload_form_new(url, file,{}),
           imgid == null: (
               log.info("name:upload,msg: 上传签名图片失败"),
               f_is_thisimage = false,
               throw {status: 500, msg: "上传流量计照片失败"}
           ), (
               log.info("name:upload, msg:上传签名图片成功"),
               //把上传成功的保存到工单中
               data.f_sign_path = data.f_sign_path,
               data.f_sign_path_name = data.signfileName,
               f_is_thisimage = true
           )
       ),null,
       f_is_thisimage == true : (
            // 把上传成功的路径保存到工单中
           data.f_meter_path_name = f_meter_path_names.substring(1,f_meter_path_names.length()),
           data.f_meter_path = f_meter_paths.substring(1,f_meter_paths.length()),
           log.info("原流量计控制器图片上传成功"),
           data.f_is_change_table=="期间有换表" : (
              data.f_last_tablebase = 0, //如果期间有过换表记录则他的上期表底数和剩余数都为0
              data.f_last_surplus_gas = 0 //如果期间有过换表记录则他的上期表底数和剩余数都为0
           ),null,
            // 如果没有换过流量计则正常保存  期间换流量计了  上传新流量计照片（新记录比旧记录的上传时间大，保证下次抄表取最新的记录）
            data.has("f_is_change_flowmeter") && data.f_is_change_flowmeter == "期间有换流量计" :(
                data.f_newmeter_paths !=null && data.f_newmeter_paths.length() != 0:(
                //当旧流量计照片上传成功后，上传旧流量计信息
                data.f_is_change_flowmeter = "期间有换流量计(旧)", //新旧流量计区分
                log.info("开始保存非民用换流量计前的照片{data}"),
                restJson = {action: "post", url: "{context.cc_base_url}/api/af-safecheck/logic/uploadCheckHandPlanItem", data:data},
                params = restTools.action(restJson),
                params.code == 500:(
                    log.info("上传旧流量计信息失败"),
                    result = {code: 500,msg: "上传换流量计前数据失败，网络故障或服务器内部错误"}
                ),(
                        // 期间新流量计信息 上传图片
                       f_meter_path_names = "", //文件名  多个文件名以逗号分割
                       f_meter_paths = "", //文件地址 多个文件以逗号分割
                       data.f_newmeter_paths.each(
                               // 本地地址中解析文件名
                               imgfileName = (row.f_meter_path).substring((row.f_meter_path).lastIndexOf("/") + 1, (row.f_meter_path).length()),
                               url = "{context.cc_base_url}/api/af-task/file/saveFileForm?module=telephone&filename={imgfileName}",
                               log.info("打印url:{url}_{imgfileName}"),
                               file = row.f_meter_path.substring(8),
                               imgid = UploadPlugin.upload_form_new(url, file,{}),
                               imgid == null: (     // 上传是否成功
                                       f_is_newimage=false,
                                       log.info("name:upload,msg: 上传图片失败"),
                                       throw {status: 500, msg: "上传流量计照片失败"}
                                 ),(
                                       f_is_newimage=true,
                                       log.info("name:upload,msg: 上传图片成功"),
                                       f_meter_path_names = "{f_meter_path_names},{imgfileName}",
                                       f_meter_paths = "{f_meter_paths},{row.f_meter_path}"
                                  )
                           ),
                           f_is_newimage == true :(
                                // 把上传成功的路径保存到工单中
                                data.f_meter_path_name = f_meter_path_names.substring(1,f_meter_path_names.length()),
                                data.f_meter_path = f_meter_paths.substring(1,f_meter_paths.length()),
                                log.info("新流量计控制器图片上传成功，保存数据到===> t_check_handplan 库中"), //上传正常抄表数据
                                data.f_flow_pressure = data.newPeriodFlowPressure,
                                data.f_flow_temperature = data.newPeriodFlowTemperature,
                                data.f_flow_standard_instant = data.newPeriodFlowStandardInstant,
                                data.f_flow_standard_total = data.newPeriodFlowStandardTotal,
                                data.f_flow_working_instant = data.newPeriodFlowWorkingInstant,
                                data.f_flow_working_total = data.newPeriodFlowWorkingTotal,
                                data.f_last_hand_date = data.f_hand_date, //上次抄表时间
                                data.f_last_tablebase = data.f_tablebase, //上期底数
                                data.f_tablebase = data.newBottomOfTheCurrentPeriod, //新流量计底数
                                data.f_last_surplus_gas = data.f_last_surplus_gas, //上期表剩余
                                data.f_surplus_gas = data.newSurPlusGasOver, //本期剩余
                                data.f_hand_date = data.f_hand_date_two, //新抄表时间
                                data.f_total_apply_gas = data.newTotalApplyGas, // 新抄表 累计标况累计用气量
                                data.f_last_flow_standard_total = 0, //期间换流量计了则最新的也就是新流量计的上期标况累计就为0
                                data.f_flow_gas_consumption = 0,
                                data.f_flow_deduction = 0,
                                data.f_flow_gas_consumption = 0,
                                data.f_difference_state = "正常",
                                data.f_difference_value = 0,
                                data.f_is_change_flowmeter = "期间有换流量计(新)",//新旧流量计区分
                                log.info("新流量计数据组装成功，保存数据到===> t_check_handplan 库中"),
                                log.info("==新流量计=保存数据===== {data}"),
                                restJson = {action: "post", url: "{context.cc_base_url}/api/af-safecheck/logic/uploadCheckHandPlanItem", data:data},
                                params = restTools.action(restJson),
                                params.data.code == 200:(
                                    result = {code: 200,msg: "上传成功"}
                                ),(
                                    result = {code: 200,msg: "上传失败，网络故障或服务器内部错误"}
                                )
                           ),(
                                log.info("上传新照片数据出错"),
                                result = {code: 500,msg: "新图片上传失败，不执行上传操作"}
                           )
                    )
                ),(
                    log.info("缺少新流量计照片信息"),
                    result = {code: 500,msg: "缺少必填参数新照片【无照片】"}
                )
            ),(
                log.info("保存非民用，期间未换流量计的信息和民用信息{data}"),
                restJson = {action: "post", url: "{context.cc_base_url}/api/af-safecheck/logic/uploadCheckHandPlanItem", data:data},
                params = restTools.action(restJson),
                params.data.code == 200:(
                    result = {code: 200,msg: "上传成功"}
                ),(
                    result = {code: 500,msg: "上传失败，网络故障或服务器内部错误"}
                )
             )
        ),(
            log.info("保存本次抄表信息照片失败"),
            result = {code: 500,msg: "图片上传失败，不执行上传操作"}
        )
),(
   log.info("缺少必填参数【无照片】"),
   result = {code: 500,msg: "缺少必填参数【无照片】，拒绝上传"}
),
log.info("完成安检抄表数据保存逻辑，返回值为：{result}"),
result
} catch (Exception e) {
    log.info("本地错误异常信息为{e}"),
    f_is_thisimage==false || f_is_newimage == false :(
        result = {code: 500,msg: "图片上传失败,请检查图片文件是否完好或丢失"}
    ),(
        result = {code:500, msg: "网络故障或服务器内部错误"}
    ),
    result
}
