//新增安检计划项 并添加活动
//data:{
//      {                                                                                   }
//      f_plan_id:'',
//      f_operator:'',
//       f_filialeid:'',
//      f_create_time:'',
//      condition:'sql条件', 查询条件
//      switchCheckAll:true/false是否有选择功能
//      checkAll:true/false, 是否选中 ‘全选按钮’
//      param[...], 选中的字段的值
//      columnName 需要做查询的字段名
//}
//
log.info("---------data:{data}"),
data.f_remark != null :(
	f_remark = data.f_remark
),(
	f_remark = ""
),

sqlstr = logic.run("ConditionalGeneration",data),
data.f_create_time=dateTools.getNow2(),
newid=commonTools.getUUID(),
tableName = "##temp_data_{commonTools.getUUID(true)}",
log.info("当前时间{data.f_create_time}"),
//查询已在安检计划中的地址数量
arrays = sql.querySQL("查询已在安检计划中的地址数量","
    select distinct ui.f_user_name,ua.f_address,ua.id,ua.f_plan_id
    from t_user_address ua  WITH(nolock)
    join t_userinfo ui  WITH(nolock) ON ui.f_userinfo_id = ua.f_userinfo_id
    left join t_userfiles uf  WITH(nolock) on uf.f_userinfo_id = ui.f_userinfo_id
    left join (
            select f_userinfoid,count(1) check_count
            from t_check_plan_item it1  WITH(nolock)
            left join t_check_plan cp1  WITH(nolock) on it1.f_plan_id=cp1.id
    		where it1.f_safecheck_type='年度普检' and cp1.f_plan_year={data.f_plan_year}
            group by f_userinfoid
        ) it2 on ui.f_userinfo_id = it2.f_userinfoid
    where {sqlstr}  and ua.f_year_plan_id is not null and (uf.f_table_state='正常' or uf.f_table_state='停用')
    AND it2.check_count is null
"),

arrs1 = jsonTools.getArray(),
arrs = jsonTools.getArray(),

arrs1Count = 0,
arrays.each(
    row.f_plan_id==null:(
        arrs1Count = arrs1Count + 1
    ),(
        arrs.put(row)
    )
),
arrs1.put({con: arrs1Count}),
// 查询当前计划数
plancon = sql.querySQL("查询当前计划数","
    select count(id) con from t_check_plan_item  WITH(nolock) where f_plan_id =  '{data.f_plan_id}'
"),
// 当前计划的原计划数
thisPlanCount = 0,
// 从go页面传来的
plancon.length() > 0 :(
    thisPlanCount = plancon[0].con
),null,
// 单条计划数必须小于5000条
commonTools.add(arrs1Count, thisPlanCount) < 5000 :(
    sql.execSQL("create_plan_item_temp","
        SELECT temp.* INTO {tableName}
        FROM (
            SELECT
            newId() id,
            ui.f_userinfo_code,
            ui.f_userinfo_id,
            ui.f_idnumber,
            ua.f_area,
            ua.f_slice_area,
            ua.f_street,
            ua.f_residential_area,
            ua.f_building,
            ua.f_unit,
            ua.f_floor,
            ua.f_room,
            {data.f_address != null:"'{data.f_address}' f_address","ua.f_address"},
            '未检' f_state,
            '未传' f_upload_state,
            '{data.f_plan_id}' f_plan_id,
            ui.f_user_name,
           {data.f_user_phone != null:"'{data.f_user_phone}' f_user_phone","ui.f_user_phone"},
            uf.f_user_type,
            '未审核' f_approved,
            '有计划安检' f_no_checkplan,
            ua.f_last_check_state,
            ua.f_last_check_date,
            '未审核' f_repair_approved,
            '{data.f_filialeid}' f_filialeid,
            '{data.f_create_time}' f_create_time,
            ui.version,
            ua.f_last_check_result,
            ui.f_comments,
            ua.id f_addressid,
            '{data.f_safecheck_type}' f_safecheck_type,
            ua.f_year_plan_id,
             '{f_remark}' f_remark,
             CONVERT(VARCHAR(32),ui.f_open_date,121) f_open_date,
             CONVERT(varchar(100),va.f_start_date,23) f_insurance_gas_date,
             't_userinfo' f_user_source,
             uf.f_meternumber f_meter_no
            FROM
                t_user_address ua WITH(nolock)
               left join t_userinfo ui WITH(nolock) ON ui.f_userinfo_id = ua.f_userinfo_id
               left join t_value_add va WITH(nolock)  on ui.f_userinfo_id = va.f_userinfo_id and f_value_name='燃气险'
               left join t_userfiles uf WITH(nolock)  on uf.f_userinfo_id = ui.f_userinfo_id
               left join (
                       select f_userinfoid,count(1) check_count
                       from t_check_plan_item it1
                       left join t_check_plan cp1   on it1.f_plan_id=cp1.id
                    where it1.f_safecheck_type='年度普检' and cp1.f_plan_year={data.f_plan_year}
                       group by f_userinfoid
                   ) it2 on ui.f_userinfo_id = it2.f_userinfoid
            WHERE {sqlstr} and ua.f_plan_id is null  and ua.f_year_plan_id is not null and check_count is null
                  and (uf.f_table_state='正常' or uf.f_table_state='停用')
        ) AS temp
    "),
    // 新增item数据
    sql.execSQL("v3_safecheck_sql", "
        INSERT INTO t_check_plan_item (
            id,
            f_userinfo_code,
            f_userinfoid,
            f_idnumber,
            f_area,
            f_slice_area,
            f_street,
            f_residential_area,
            f_building,
            f_unit,
            f_floor,
            f_room,
            f_address,
            f_state,
            f_upload_state,
            f_plan_id,
            f_user_name,
            f_user_phone,
            f_user_type,
            f_approved,
            f_no_checkplan,
            f_last_check_state,
            f_last_check_date,
            f_repair_approved,
            f_filialeid,
            f_create_time,
            version,
            f_last_check_result,
            f_comments,
            f_addressid,
            f_safecheck_type,
            f_year_plan_id,
            f_remark,
            f_open_date,
            f_insurance_gas_date,
            f_user_source,
            f_meter_no
            ) SELECT
            * from {tableName}
            "
    ),
    log.info("结果---{arrs}"),
    //最后修改地址中的安检计划id
    sql.execSQL("v3_safecheck_sql", "
        update t_user_address set f_plan_id='{data.f_plan_id}',f_check_version=0
        where  id in ( select f_addressid from {tableName} )
    "
    ),
    sql.execSQL("delete_temp_data","
        drop table {tableName}
    "),
    {code:200,result:arrs,resultno:arrs1}
),(
    {code:501,result:arrs,resultno:arrs1,msg: "派发失败，每个计划下最多5000户，您本次添加了{arrs1[0].con}户，请分为多个计划"}
)

