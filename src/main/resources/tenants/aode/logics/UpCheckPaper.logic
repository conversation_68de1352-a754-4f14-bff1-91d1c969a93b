log.info("uploadPaper:------进入数据保存过程-----"),
log.info( "进入上传: {data}"),
data = data.row,
// 上传图片
url = "{context.cc_base_url}/api/af-safecheck",
resolve = UploadPic.uploadPic(url,data),
resolve == null :(
    log.info( "上传图片失败"),
    message = UploadPlugin.getResObj().message,
    return {code: 710, msg: message}
),null,
log.info( "上传图片成功"),
paperState="正常",
isCheck = resolve.data.paper.f_no_checkplan != "无计划安检" && resolve.data.paper.f_no_checkplan != "复检",
isCheck == true :(
    d0 = {
        data: {
            id: resolve.data.paper.f_check_item_id
        }
    },
    log.info( "参数===》{d0}"),
    resJson = {action: "post", url: "{context.cc_base_url}/api/af-safecheck/sql/GetCheckItemState",data:d0.toString()},
    res = restTools.action(resJson),
    log.info( "结果===》{res}"),
    res.data.length() == 0 :(
        log.info( "服务器安检单不存在"),
        return {code: 711, msg: "安检单已被移除"}
    ),null,
    lastState = res.data[0].f_last_check_state,
    paperState = res.data[0].f_state,
    paperDate = res.data[0].f_last_check_date,
    isUploadDay = res.data[0].isupload_day,
    // 最后一次上传记录是到访不遇，并且截止到当前时间不满1天，并且本次提交也是到访不遇禁止上传
    paperState == "已检" && lastState == "到访不遇" && resolve.data.paper.f_entry_status == "到访不遇" && isUploadDay < 1 :(
        log.info("上次安检是到访不遇，距离现在不超过1天，禁止提交"),
        return {code: 308, msg: "上次安检是到访不遇,距离现在不超过1天，禁止提交"}
    ),null,
    paperStateupload = res.data[0].f_upload_state,
    paperState == "已检" && lastState == "入户" && paperStateupload == "已传" :(
        sql.action({sql: "update t_check_plan_item set f_state='已检',f_upload_state='已传',f_flag=0
            where id = '{data.f_check_item_id}'", cmd: "cmd"
        }),
        return {code:200, msg:"该单已成功入户，请勿重复上传!"}
    ),
    paperState == "已检" && lastState == "到访不遇" && paperDate == resolve.data.paper.f_offsite_time :(
        return {code:200, msg:"该单已上传，请勿重复上传!"}
    ),null,
    paperState == "作废" :(
        return {code: 309, msg: "作废"}
    ),null,
    // 上传工单
    data.f_repaired = "未修",
    data.f_repaired_uploaded = "已传",
    taskData = {
        "f_task_type": "CommonRunLogic",
        "f_task_class": "com.af.v4.impl.CommonRunLogicHandlerImpl",
        "f_user_id": resolve.data.paper.f_userinfoid,
        "f_tag1": "UpdateCheckPaper", // logic名称
        "f_service_name": "af-safecheck", // 服务名称
        "f_task_name": "{resolve.data.paper.f_checker_id}_{resolve.data.paper.f_userinfoid}_{resolve.data.paper.f_offsite_time}", // 保证唯一
        "f_data": resolve.toString() // data数据
    },
    restJson = {action: "post", url: "{context.cc_base_url}/api/af-task/logic/taskSend", data:taskData.toString()},
    log.info("开始上传工单：{data.toString()}"),
    params = restTools.action(restJson),
    log.info("上传结果：{params}"),
    params.code == 200 :(
        check_version=0,
        resolve.data.paper.f_entry_status=="到访不遇":(
            check_version=1
        ),null,
        sql.action({sql: "update t_check_plan_item set f_approved = null,f_state='已检',f_upload_state='已传',f_flag=0,
            f_check_version=(case when f_check_version is null then 1
            else (f_check_version+{check_version}) end)
            where id = '{data.f_check_item_id}'", cmd: "cmd"
        }),
        log.info("上传data：2"),
        return {code:200, msg:"上传成功"}
    ),(
        return {code: 712, msg: "网络异常"}
    )
),(
    data.f_repaired = "未修",
    data.f_repaired_uploaded = "已传",
    restJson = {action: "post", url: "{context.cc_base_url}/api/af-safecheck/logic/UpdateCheckPaper", data:resolve.toString()},
    log.info("开始上传工单：{data.toString()}"),
    params = restTools.action(restJson),
    log.info("上传结果：{params}"),
    params.code == 200 :(
        log.info("上传data：1"),
        sql.action({sql: "update t_check_plan_item set f_state='已检',f_upload_state='已传',f_flag=0
            where id = '{data.f_check_item_id}'", cmd: "cmd"
        }),
        log.info("上传data：2"),
        return {code:200, msg:"上传成功"}
    ),(
        return {code: 712, msg: "网络异常"}
    )
)
