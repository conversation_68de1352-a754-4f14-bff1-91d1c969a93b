//根据参数做对安检计划项的增删改
//data:{
//      {                                                                                   }
//      f_plan_id:'数据',
//      f_operator:'',
//      condition:'sql条件', 查询条件
//      switchCheckAll:true/false是否有选择功能
//      checkAll:true/false, 是否选中 ‘全选按钮’
//      param[...], 选中的字段的值
//      columnName 需要做查询的字段名
//}
//
sqlstr = logic.run("ConditionalGeneration",data),
log.info("执行修改====data:{data}"),
data.f_create_time=dateTools.getNow2(),
newid=commonTools.getUUID(),
//修改计划项对应的计划id
sql.execSQL("v3_safecheck_sql", "update t_check_plan_item set f_plan_id = '{data.f_plan_id}' from t_check_plan_item it where {sqlstr} "),
//将对应的地址表中f_plan_id也进行修改
sql.execSQL("v3_safecheck_sql", "update t_user_address set f_plan_id='{data.f_plan_id}' from t_user_address ua join t_check_plan_item it on ua.id=it.f_addressid where {sqlstr} "),
// 添加派单动作，注意继承关系
sql.execSQL("v3_safecheck_sql", "
        INSERT INTO t_check_acitivity(serviceid,f_service_acitivity_type,f_batch_number)
            SELECT
                id,
                '转单',
                '{newid}'
             from t_check_plan_item it where {sqlstr}
"),
sql.execSQL("v3_safecheck_sql", "
        INSERT INTO t_check_change (id,serviceid,f_service_acitivity_type,f_change_date,f_meetunit,f_operator,f_change_planid,f_change_planname,f_batch_number)
         SELECT
            sa.id,
            sa.serviceid,
            sa.f_service_acitivity_type,
            '{data.f_create_time}',
            f_checker,
            '{data.f_operator}',
            cp.id,
            cp.f_plan_name,
            '{newid}'
          from t_check_acitivity sa
          join t_check_plan_item it on sa.serviceid=it.id
          join t_check_plan cp on it.f_plan_id=cp.id
           where sa.f_batch_number='{newid}'
"),
{code:200}
