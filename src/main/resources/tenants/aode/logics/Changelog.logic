log.info("信息变更数据:{data}"),
userinfo= sql.querySQL("v3_safecheck_sql", "
    select * from t_userinfo where f_userinfo_id = '{data.paper.f_userinfoid}'
"),
userinfo.length() > 0:(
    userinfo[0].f_user_phone!=data.paper.f_user_phone:(
     sql.execSQL("v3_safecheck_sql", "update t_userinfo set f_user_phone = '{data.paper.f_user_phone}' where f_userinfo_id = '{data.paper.f_userinfoid}'"),
    log.info("手机号被修改了"),
        record={
             f_userinfo_id:data.paper.f_userinfoid,
             f_describe:"{data.paper.f_checker_name}对用户{userinfo[0].f_user_name}进行档案变更",
             f_operat_type: "档案变更",
             f_state:"有效",
             f_change_type: "档案变更",
             f_change_state: "有效",
             f_operate_date:dateTools.getNow2(),
             f_comments:"安检信息修改"
             },
    change_id= jsonTools.convertToJson(entity.partialSave("t_record",record)).id,
    recordinfo={
     f_change_id:change_id,
     f_userinfo_id:data.paper.f_userinfoid,
     f_submit_ename:data.paper.f_checker_name, //修改人
     f_submit_date:dateTools.getNow2()
    },
        userinfo.length() > 0 :(
        userinfo.each(
            row.f_user_phone!=data.paper.f_user_phone:(
            recordinfo.f_field_name="用户电话",
            recordinfo.f_field_ename="f_user_phone",
            recordinfo.f_used_content=row.f_user_phone,//旧值
            recordinfo.f_new_content=data.paper.f_user_phone,//新值
            entity.partialSave("t_infochange",recordinfo)
            ),null
        )
        ),null
    ),null
),null,
200
