<?xml version="1.0"  encoding='utf-8'?>
<!DOCTYPE hibernate-mapping PUBLIC
        "-//Hibernate/Hibernate Mapping DTD 3.0//EN"
        "http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd">
<hibernate-mapping>
    <!--计划项-->
    <class entity-name="t_plan_item_meter" table="t_plan_item_meter">
        <id name="id" type="string">
            <generator class="assigned"/>
        </id>
        <!-- 档案id -->
        <property name="f_userinfoid" type="int"/>
        <!-- 表编号 -->
        <property name="f_userid" type="string"/>
        <!-- 表号 -->
        <property name="f_meter_no" type="string"/>
        <!-- 表封号 -->
        <property name="f_metertitles" type="string"/>
        <!-- 生产厂家 -->
        <property name="f_maker" type="string"/>
        <!--表厂别名-->
        <property name="f_aliasname" type="string"/>
        <!-- 表型 膜孔大小-->
        <property name="f_diaphgram_size" type="string"/>
        <!-- 卡号 -->
        <property name="f_card_id" type="string"/>
        <!-- 左右表 -->
        <property name="f_flow_direction" type="string"/>
        <!-- 机表号/流量计号 -->
        <property name="f_device_id" type="string"/>
        <!-- 生产日期 -->
        <property name="f_making_date" type="int"/>
        <!-- 流量计型号 -->
        <property name="f_traffic_model" type="string"/>
        <!-- 表/流量计 -->
        <property name="f_meter_type" type="string"/>
        <!-- 表底数 -->
        <property name="f_prior_reading" type="string"/>
        <!--初始气表底数 -->
        <property name="f_initial_base" type="string" />
        <!-- 安装位置 -->
        <property name="f_weizhi" type="string"/>
        <!--当前表累计购气量-->
        <property name="f_metergasnums" type="string"/>
        <!-- 版本 -->
        <property name="version" type="int"/>
        <!-- 关联 -->
        <property name="f_plan_item_id" type="string"/>
    </class>
</hibernate-mapping>
