<?xml version="1.0"  encoding='utf-8'?>
<!DOCTYPE hibernate-mapping PUBLIC
        "-//Hibernate/Hibernate Mapping DTD 3.0//EN"
        "http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd">
<hibernate-mapping>
    <!--定时任务通用执行-->
    <class entity-name="t_safe_queue_task" table="t_safe_queue_task">
        <!--任务编号-->
        <id name="id" type="int">
            <generator class="native"></generator>
        </id>
        <!--任务描述-->
        <property name="f_task_description" type="string"/>
        <!--任务参数-->
        <property name="f_params" type="string" length="8000" not-null="true"/>
        <!--目标url-->
        <property name="f_target_url" type="string" length="1000" not-null="true"/>
        <!--请求方式  GET POST-->
        <property name="f_request_methed" type="string" length="20" not-null="true"/>
        <!--任务状态  0待执行 1完成  2失败-->
        <property name="f_task_status" type="int" not-null="true">
            <column name="f_task_status" default="0"/>
        </property>
        <!--发送次数-->
        <property name="f_retry_num" type="int">
            <column name="f_retry_num" default="0"/>
        </property>
        <!--任务失败原因-->
        <property name="f_error_cause" type="string" length="6000"/>
        <!--任务创建时间-->
        <property name="f_create_date" type="date" not-null="true">
            <column name="f_create_date" default="getDate()"/>
        </property>
        <!--任务执行时间-->
        <property name="f_execute_date" type="date"/>
        <!--任务创建人-->
        <property name="f_task_creater" type="string" length="255"/>
        <!--任务人id-->
        <property name="f_task_creater_id" type="string" length="255"/>

    </class>
</hibernate-mapping>
