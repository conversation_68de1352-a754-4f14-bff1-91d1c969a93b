<?xml version="1.0"  encoding='utf-8'?>
<!DOCTYPE hibernate-mapping PUBLIC 
	"-//Hibernate/Hibernate Mapping DTD 3.0//EN"
	"http://hibernate.sourceforge.net/hibernate-mapping-3.0.dtd">
<hibernate-mapping>
    <!--图片/录音-->
    <class   entity-name="t_picture" table="t_picture" >
        <!-- id -->
        <id name="id" type="int" column="id">
            <generator class="native"></generator>
        </id>
		<!--图片 -->
		<property name="f_fittings" type="string" length='50'/>
		<!--录音 -->
		<property name="f_record" type="string" length='50'/>
		<!-- 维修id -->
		<property name="f_maintain_id" type="string" length='50'/>
		<!--备注-->
		<property name="f_remarks" type="string" length="100"/>
	</class>
</hibernate-mapping>
