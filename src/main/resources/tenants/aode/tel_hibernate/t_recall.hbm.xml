<?xml version="1.0"  encoding='utf-8'?>
<!DOCTYPE hibernate-mapping PUBLIC
		"-//Hibernate/Hibernate Mapping DTD 3.0//EN"
		"http://hibernate.sourceforge.net/hibernate-mapping-3.0.dtd">
<hibernate-mapping>
	<!--撤回信息-->
	<class   entity-name="t_recall" table="t_recall" >
		<!-- id -->
		<id name="id" type="int">
			<generator class="assigned"></generator>
		</id>
		<!--工单编号 -->
		<property name="serviceid" type="int" length="100"/>
		<!-- 用户微信的openid -->
		<property name="f_open_id" type="string" length='100'/>
		<!--活动编号 -->
		<property name="f_service_acitivity_id" type="string" length='20'/>
		<!-- 活动类型 -->
		<property name="f_service_acitivity_type" type="string" length="20"/>
		<!--撤回人-->
		<property name="f_recall_name" type="string" length="50"/>
		<!--撤回日期-->
		<property name="f_recall_date" type="timestamp" length="20"/>
		<!-- 批号 -->
		<property name="f_batch_number" type="string" length="50"/>
	</class>
</hibernate-mapping>
