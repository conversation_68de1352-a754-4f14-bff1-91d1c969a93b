<?xml version="1.0"  encoding='utf-8'?>
<!DOCTYPE hibernate-mapping PUBLIC 
	"-//Hibernate/Hibernate Mapping DTD 3.0//EN"
	"http://hibernate.sourceforge.net/hibernate-mapping-3.0.dtd">
<hibernate-mapping>
    <!--文字客服日志-->
    <class   entity-name="t_texting_log" table="t_texting_log" >
        <!-- id -->
		<id name="id" type="int" column="id">
			<generator class="native"></generator>
        </id>
        <!-- 会话id -->
        <property name="f_session_id" type="string" length="50"/>
        <!-- 时间 -->
        <property name="f_dt" type="string" length="20"/>
        <!-- 消息内容 -->
        <property name="f_content" type="string" length="200"/>
		<!--缩略图  -->
		<property name="f_thumbnail" type="string" length="100" /> 
		<!-- 消息类型 -->
		<property name="f_msg_type" type="string" length="10"/>
		<!-- 消息方向 -->
		<property name="f_msg_direction" type="string" length="10"/>
		<!-- 话务员 -->
		<property name="f_attendant" type="string" length="10"/>
		<!-- openid -->
		<property name="f_open_id" type="string" length="50"/>
		<!-- 用户id -->
		<property name="f_user_id" type="string" length="50"/>
		<!-- 用户名 -->
		<property name="f_user_name" type="string" length="20"/>
        <!--备注 -->
		<property name="f_remark" type="string" length="100"/>
		<!-- 视频标题 -->
		<property name="f_title" type="string" length="100"/>
		<!-- 视频描述 -->
		<property name="f_description" type="string" length="200"/>
		<!-- 话务身份：话务员|维修员-->
		<property name="f_text_handler" type="string" length="10"/>
		<!-- 维修员姓名 -->
		<property name="f_repairman" type="string" length="10"/>
		<!-- 工单id -->
		<property name="f_service_id" type="string" length="30"/>
	</class>
</hibernate-mapping>
