<?xml version="1.0"  encoding='utf-8'?>
<!DOCTYPE hibernate-mapping PUBLIC
        "-//Hibernate/Hibernate Mapping DTD 3.0//EN"
        "http://hibernate.sourceforge.net/hibernate-mapping-3.0.dtd">
<hibernate-mapping>
    <!--工单-->
    <class   entity-name="t_serviceworkorder" table="t_serviceworkorder" >
        <!-- id -->
        <id name="id" type="int" column="id">
            <generator class="native"></generator>
        </id>

        <!-- 版本号，用于手机端确定工单是否修改过 -->
        <property name="service_version" type="int"></property>
        <!-- 工单id备份，用于android端 -->
        <property name="id_back" type="int"></property>

        <!-- 工单编号 -->
        <property name="f_service_id" type="string" length="255"/>
        <!-- 工单状态: 撤销: 工单撤销, 空: 其它 -->
        <property name="f_state" type="string" length="255"/>
        <!-- 用户微信的openid -->
        <property name="f_open_id" type="string" length='100'/>
        <!--工单生成时间 -->
        <property name="f_created_date" type="string" length='255'/>
        <!--工单预约时间 -->
        <property name="f_yuyue_date" type="string" length='255'/>
        <!--工单类型 -->
        <property name="f_workorder_type" type="string" length='255' />
        <!-- 流程实例id -->
        <property name="f_processid" type="string" length="255"/>
		<!-- 用户编号  -->
		<property name="f_userinfo_id" type="int" />
		<!-- 用户编号 code  -->
		<property name="f_userinfo_code" type="string" length='255'/>
        <!-- 用户姓名/单位名称 -->
        <property name="f_user_name" type="string" length="100"/>
        <!--地址-->
        <property name="f_address" type="string" length="100"/>
        <!--来电电话-->
        <property name="f_phone" type="string" length="100"/>
        <!--联系电话-->
        <property name="f_contact_phone" type="string" length="100"/>
        <!--报修类型-->
		<property name="f_repairtype" type="string" length="255"/>
        <!--话务员-->
        <property name="f_attendant" type="string" length="100"/>
        <!-- 卡号 -->
        <property name="f_card_id" type="string" length="100"/>
        <!--派单人-->
        <property name="f_single_man" type="string" length="255"/>
        <!--建议-->
        <property name="f_advice" type="string" length="100"/>
        <!--备注-->
        <property name="f_remarks" type="string" length="100"/>
        <!--维修员-->
        <property name="f_order_man" type="string" length="255"/>
        <!--所在位置，维修员/站点/中心-->
        <property name="f_department" type="string" length="255"/>
        <!-- 工单来源 -->
        <property name="f_source" type="string" length="200"/>
        <!-- 故障明细,json格式:[{f_failure_type:故障类型,f_equipment:设备品牌,f_failure:['故障']}] -->
        <property name="failure" type="string" length="2000"/>
       <!-- 录音ID -->
		<property name="f_record_sound" type="string" length="100"/>
        <!-- 报修单表对应一次活动一对多 -->
        <set name="serviceacitivity" cascade="all-delete-orphan" inverse="true">
            <key column="serviceid" on-delete="cascade" />
            <one-to-many entity-name="t_service_acitivity" not-found="exception" />
        </set>


        <!-- 分公司 -->
        <property name="f_filiale" type="string" length="200"/>
        <!-- 分公司id -->
        <property name="f_filiale_id" type="string" length="50"/>
        <!-- 组织(分公司)id -->
        <property name="f_orgid" type="string" length="50"/>
        <!-- 组织(分公司)操作人的公司 -->
        <property name="f_orgname" type="string" length="50"/>
        <!--操作人的部门 -->
        <property name="f_depname" type="string" length="50"/>
        <!-- 操作人的部门id -->
        <property name="f_depid" type="string" length="50"/>
        <!--操作人 -->
        <property name="f_operator" type="string" length="50"/>
        <!-- 操作人的id -->
        <property name="f_operatorid" type="string" length="50"/>
        <!--公司机构ids -->
        <property name="f_filialeids" type="string" length="200" />
        <!-- 营业网点 -->
        <property name="f_outlets" type="string" length="50"/>
        <!-- 组织机构 -->
        <property name="f_orgstr" type="string" length="200"/>
        <!-- PC端特殊字段 -->
        <!-- 该工单是否退回: null 没退回  true 已退回 -->
        <property name="f_backsuccess" type="string" length="10"/>
        <!-- 手机端特殊字段 -->
        <!-- 是否上传成功: 成功/失败 -->
        <property name="f_success" type="string" length="10"/>
        <!-- 工单状态:  null/未完成/已完成/-->
        <property name="f_orderstatus" type="string" length="10"/>
        <!-- 是否延期申请提交成功: 成功/失败 -->
        <property name="f_delaysuccess" type="string" length="10"/>
        <!-- 工单json串形式，手机端工单整体内容按json串存放 -->
        <property name="f_json" type="string"/>
        <!-- 催单标准位是否被用戶催单：false/true(不催单、催单) -->
        <property name="f_remindersign" type="string" length="10"/>
        <!-- 催单内容 -->
        <property name="f_reminderdata" type="string" length="2000"/>
        <!--是否延期-->
        <property name="f_isdelay" type="string" length="100"/>
        <!-- 地址串   绑定地址用-->
        <property name="f_addressid" type="int"/>
        <!-- 工单申请id-->
        <property name="f_orderservice_id" type="string" length="50"/>
        <!--区分集中和散户-集中派工/散户派工-->
        <property name="f_change_tag" type="string" length="50"/>
		<!--工单回访状态 -->
		<property name="f_revisit" type="string"  length="100"/>
		<!--工单回访人员 -->
		<property name="f_visit_member" type="string"  length="100"/>
		<!--工单回访时间 -->
		<property name="f_revisit_date" type="string"  length="100"/>
		<!--工单回访内容 -->
		<property name="f_user_opinion" type="string"  length="2000"/>

    </class>
</hibernate-mapping>
