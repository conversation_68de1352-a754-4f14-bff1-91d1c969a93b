<?xml version="1.0"  encoding='utf-8'?>
<!DOCTYPE hibernate-mapping PUBLIC 
	"-//Hibernate/Hibernate Mapping DTD 3.0//EN"
	"http://hibernate.sourceforge.net/hibernate-mapping-3.0.dtd">
<hibernate-mapping>
    <!--话务员记录表-->
    <class   entity-name="t_chatrecord" table="t_chatrecord" >
        <!-- id -->
		<id name="id" type="int" column="id">
        	<generator class="native"></generator>
        </id>
        <!-- 话务员编号 -->
        <property name="f_operator_id" type="string" length="20"/>
        <!-- 话务员姓名 -->
        <property name="f_operator_name" type="string" length="20"/>
        <!-- 话务类型 -->
        <property name="f_traffic_type" type="string" length="20"/>
		<!-- 接听开始时间 -->
		<property name="f_answer_date" type="timestamp"/>
		<!-- 接听结束时间 -->
		<property name="f_end_date" type="timestamp"/>
		<!-- 来电/外拨电话 -->
		<property name="f_call_phone" type="string" length="20"/>
		<!-- 是否接通 -->
		<property name="f_whether_through" type="string" length="10"/>
		<!-- 话务id -->
		<property name="f_traffic_id" type="int" />
		<!--用户基本信息 -->
        <!--表编号 -->
		<property name="f_user_id" type="int"/>
		<!--用户编号 -->
		<property name="f_userinfo_id" type="int"/>
        <!--用户姓名 -->
		<property name="f_user_name" type="string" length="100"/>
		<!--地址-->
		<property name="f_address" type="string" length="100"/>
	</class>
</hibernate-mapping>
