<?xml version="1.0"  encoding='utf-8'?>
<!DOCTYPE hibernate-mapping PUBLIC
		"-//Hibernate/Hibernate Mapping DTD 3.0//EN"
		"http://hibernate.sourceforge.net/hibernate-mapping-3.0.dtd">
<hibernate-mapping>
	<!--一次维修信息-->
	<class entity-name="t_telyuyuefinish" table="t_telyuyuefinish" >
		<!-- id -->
		<id name="id" type="int">
			<generator class="assigned"></generator>
		</id>
		<!--工单编号 -->
		<property name="serviceid" type="int" length="100"/>
		<!--工单类型 -->
		<property name="f_workorder_type" type="string" length='255' />
		<!-- 一次维修id备份，用于android端 -->
		<property name="id_back" type="int"/>
		<!--活动编号 -->
		<property name="f_service_acitivity_id" type="string" length='50'/>
		<!--维修单编号 -->
		<property name="f_service_work_id" type="string" length='50'/>
		<!-- 日期 -->
		<property name="f_date" type="string" length="100"/>
		<!-- 人员 -->
		<property name="f_name" type="string" length="100"/>
		<!-- 到达时间 -->
		<property name="f_date_come" type="string" length="100"/>
		<!-- 离开时间 -->
		<property name="f_date_leave" type="string" length="100"/>
		<!-- 安检单ID  -->
		<property name="f_checkpaper_id" type="string" />
		<!--备注-->
		<property name="f_remarks" type="string" length="1000"/>
	</class>
</hibernate-mapping>
