<?xml version="1.0"  encoding='utf-8'?>
<!DOCTYPE hibernate-mapping PUBLIC 
	"-//Hibernate/Hibernate Mapping DTD 3.0//EN"
	"http://hibernate.sourceforge.net/hibernate-mapping-3.0.dtd">
<hibernate-mapping>
	<!-- 管理成本 -->
    <class entity-name="t_magprime" table="t_matter" >
        <!-- id -->
        <id name="id" type="int" column="id">
	        <generator class="native"></generator>
        </id>
        <!--类型名称 -->
		<property name="f_manage_name" type="string" length="100"/>
        <!--单价（元） -->
		<property name="f_price" type="java.math.BigDecimal">
			<column name="f_price" default="0" scale="2"></column>
		</property>
		<!--单位-->
		<property name="f_unit" type="string" length="200"/>
		<!--备注-->
		<property name="f_remarks" type="string" length="200"/>
	</class>
</hibernate-mapping>
