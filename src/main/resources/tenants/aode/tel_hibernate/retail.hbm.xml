<?xml version="1.0"  encoding='utf-8'?>
<!DOCTYPE hibernate-mapping PUBLIC 
	"-//Hibernate/Hibernate Mapping DTD 3.0//EN"
	"http://hibernate.sourceforge.net/hibernate-mapping-3.0.dtd">
<hibernate-mapping package="test">
	<!--散户安装-->
	<class entity-name="t_retail" table="t_retail" dynamic-update="true">
		 <id name="id" type="int">
			<generator class="assigned"></generator>
		</id>
		<!-- 类型 -->
		<property name="f_type" type="string" length="100" />
		<!-- 中间表id -->
		<property name="f_serviceRepair_id" type="string" length="100"/>
		 <!--工单编号 -->
		<property name="serviceid" type="string" length="100"/>
		<!--表具位置  -->
		<property name="f_meter_position" type="string" length="100"/>
		<!--立管情况  -->
		<property name="f_li_result" type="string" length="100"/>
		<!--是否开口  -->
		<property name="f_is_open" type="string" length="100"/>
		<!--处理结果 -->
		<property name="f_result" type="string" length="100"/>
		<!--气密性检查人员 组别 -->
		<property name="f_men_team" type="string" length="100"/>
		<!--气密性检查人员  -->
		<property name="f_men" type="string" length="100"/>
		<!--备注  -->
		<property name="f_remarks" type="string" length="100"/>
		<!-- 中间表 故障详情·设备维修·灶具 一对多 -->
		<set name="details" cascade="all-delete-orphan" >
          <key column="f_service_id" on-delete="noaction" /> 
          <one-to-many entity-name="t_details" not-found="exception" /> 
        </set>
		
		<!-- 状态 -->
		<property name="f_status" type="string" length="50" />
		<!-- 录入时间 -->
		<property name="f_repair_date" type="timestamp"/>
		<!-- 录入人 -->
		<property name="f_repair_man" type="string" length="50" />
		<!--网点,上级组织名称-->
		<property name="f_parentname" type="string" length="100"/>
		<!-- 分公司 -->
		<property name="f_filiale" type="string" length="100"/>
	
	</class>
</hibernate-mapping>
