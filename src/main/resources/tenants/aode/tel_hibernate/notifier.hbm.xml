<?xml version="1.0"  encoding='utf-8'?>
<!DOCTYPE hibernate-mapping PUBLIC 
	"-//Hibernate/Hibernate Mapping DTD 3.0//EN"
	"http://hibernate.sourceforge.net/hibernate-mapping-3.0.dtd">
<hibernate-mapping>
    <!--金剑平台通知表-->
    <class entity-name="t_notifier" table="t_notifier" >
        <!-- id 代理主键 -->
		<id name="id" type="int" column="id">
			<generator class="native"></generator>
        </id>
		<!-- 工作流定义活动 -->
		<property name="activity" type="string" length='80'/>
		<!-- actor -->
		<property name="actor" type="string" length='50'/>
		<!-- 金剑登录用户的guid，不是用户名 -->
		<property name="f_user_guid" type="string" length='50'/>
		<!-- 用户名 -->
		<property name="f_user_id" type="string" length='50'/>
		<!-- 通知消息个数 -->
		<property name="f_msg_cnt" type="int" />
		<!-- 通知标题 -->
		<property name="f_title" type="string" length='100'/>		
		<!-- 通知内容 -->
		<property name="f_content" type="string" length='100'/>		
		<!-- 重定向url -->
		<property name="f_redirect_url" type="string" length='200'/>		
		<!-- 通知数据状态描述 -->
		<property name="f_status" type="string" length='50'/>				
	</class>
</hibernate-mapping>
