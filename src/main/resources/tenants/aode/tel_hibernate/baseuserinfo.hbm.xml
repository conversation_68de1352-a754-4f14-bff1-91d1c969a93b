<?xml version="1.0"  encoding='utf-8'?>
<!DOCTYPE hibernate-mapping PUBLIC
	"-//Hibernate/Hibernate Mapping DTD 3.0//EN"
	"http://hibernate.sourceforge.net/hibernate-mapping-3.0.dtd">
<hibernate-mapping package="test">

	<class entity-name="t_baseuserinfo" table="t_baseuserinfo" dynamic-update="true">
		<!--id-->
	    <id name="id" type="int">
			<generator class="foreign">
				<param name="property">t_servicework</param>
			</generator>
		</id>
		<!-- 用户编号  id-->
		<property name="f_userinfo_id" type="int" />
		<!-- 用户编号 code -->
        <property name="f_userinfo_code" type="string" length="100"/>
		<!-- 表号 -->
		<property name="f_meternumber" type="string" length="50"/>
		<!-- 气表底数 -->
		<property name="f_meter_base" type="string" length="50"/>
        <!-- 用户姓名 -->
        <property name="f_user_name" type="string" length="100"/>
        <!-- 用户类型 -->
        <property name="f_user_type" type="string" length="100"/>
		<!-- 非民用用户类型 选择工业用户、商业用户 -->
		<property name="f_npuser_type" type="string" length="100"/>
		<!-- 住户类型 -->
		<property name="f_house_type" type="string" length="100"/>
        <!-- 来电电话 -->
		<property name="f_linkphone" type="string" length="100"/>
	  	<!-- 联系电话 -->
		<property name="f_user_phone" type="string" length="100"/>
		<!-- 地址 -->
		<property name="f_address" type="string" length="100"/>
		<!-- 用气性质 -->
		<property name="f_gasproperties" type="string" length="100"/>
		<!-- 气表品牌 -->
		<property name="f_gasbrand_name" type="string" length="100"/>
		<!-- 气表型号 -->
		<property name="f_gasmodel_name" type="string" length="100"/>
		<!-- 气表品牌id -->
		<property name="f_gasbrand_id" type="int"/>
		<!-- 气表型号id -->
		<property name="f_gasmodel_id" type="int"/>
		<!-- 气价id -->
		<property name="f_pricename_id" type="int"/>
		<!-- 气价类型 -->
		<property name="f_price_type" type="string" length="100"/>
		<!-- 气价名称 -->
		<property name="f_pricename" type="string" length="100"/>
		<!-- 左右表 -->
		<property name="f_aroundmeter" type="string" length="100"/>
		<!-- 左右表 -->
		<property name="f_position" type="string" length="100"/>
		<!-- 置换通气单  需要更新档案的  数据   JSON串 -->
		<property name="f_gascheckdata" type="string" length="2000"/>
		<!-- 气表底数 -->
		<property name="f_initial_base" type="string" length="100"/>
		<!-- 身份证 -->
		<property name="f_shenfenzheng" type="string" length="100"/>
		<!-- 合同号 -->
		<property name="f_hetonghao" type="string" length="100"/>
		<!-- 进气铅封号 -->
		<property name="f_airintake_seal_no" type="string" length="100"/>
		<!-- 出气铅封号 -->
		<property name="f_airouttake_seal_no" type="string" length="100"/>
		<!-- 备注 -->
		<property name="f_remarks" type="string" length="1000"/>
	</class>
</hibernate-mapping>
