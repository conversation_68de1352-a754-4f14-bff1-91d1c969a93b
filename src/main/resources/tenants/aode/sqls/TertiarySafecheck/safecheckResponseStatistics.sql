-- 安检从派单到上传的时间间隔。只要派单了，一直未处理，则取当前时间和派单时间的时间间隔
select
    convert(decimal(18,2),isnull(times,0)/isnull(count_end,0)) avg_count, -- 已下发的总计划数和总用时求的平均值
    isnull(count_end,0) count_end, -- 已下发的总计划数
    isnull(danwei,'无') danwei, --单位
    isnull(times,0) times, --安检总时长
    isnull(overdue_count,0) overdue_count -- 超时总计划数
from (
    select count(1) count_end,-- ！总计划数
        case when sum (CAST(DATEDIFF( n, cp.f_issue_time, isnull(cpp.f_upload_date,getdate()) ) AS  bigint)) >= 60 then --分钟
            case when sum (CAST(DATEDIFF( hh, cp.f_issue_time, isnull(cpp.f_upload_date,getdate()) ) AS  bigint)) >= 24 then  -- 小时
                sum (CAST(DATEDIFF( hh, cp.f_issue_time, isnull(cpp.f_upload_date,getdate()) ) AS  bigint))/24  -- 天
            else sum (CAST(DATEDIFF( hh, cp.f_issue_time, isnull(cpp.f_upload_date,getdate()) ) AS  bigint)) end
        else sum (CAST(DATEDIFF( n, cp.f_issue_time, isnull(cpp.f_upload_date,getdate()) ) AS  bigint)) end  times, -- ！总时长

        case when sum (CAST(DATEDIFF( n, cp.f_issue_time, isnull(cpp.f_upload_date,getdate()) ) AS  bigint)) >= 60 then --分钟
            case when sum (CAST(DATEDIFF( hh, cp.f_issue_time, isnull(cpp.f_upload_date,getdate()) ) AS  bigint)) >= 24 then  -- 小时
                '天'  -- 天
            else '时' end
        else '分' end  danwei, -- ！单位
       sum(case when CAST(DATEDIFF( hh, cp.f_issue_time, isnull(cpp.f_upload_date,getdate()) ) AS  bigint) >= {overdue_hh} then 1 else 0 end) overdue_count --超时总计划数
    from
        t_check_plan cp with(nolock)
        left join t_check_plan_item cpi with(nolock) ON cp.id = cpi.f_plan_id and cpi.f_filialeid in {f_filiale_id}
        left join t_check_paper cpp with (nolock ) ON cpi.id = cpp.f_check_item_id
        where cp.f_filialeid in {f_filiale_id}  and  {condition}  and cp.f_issued = '是'
) t1 order by overdue_count
