select
       ui.f_userinfo_code,
       ui.f_user_name,
       ui.f_user_phone,
       uf.f_user_type,
       uad.f_address,
       case when t1.f_entry_status = '入户' then '已检' else '未检' end ischeck,
       t1.*,
       cpl.*
from
        t_userinfo ui  with(nolock) left join
        t_user_address uad  with(nolock) ON ui.f_userinfo_id = uad.f_userinfo_id
        left join t_userfiles uf  with(nolock) ON ui.f_userinfo_id = uf.f_userinfo_id
        left join (
            -- 查询每个用户的最后一次入户状态
            select cpp.f_entry_status,cpp.f_offsite_time,cpi.f_userinfoid,cpi.f_plan_id,cpp.f_upload_date
    from (
                select *,row_number() over(partition by f_userinfoid order by f_offsite_time desc) rn from t_check_paper t_check_paper with(nolock)
                where f_entry_status = '入户' and f_filialeid  in  {f_filialeid}
                ) cpp left join  t_check_plan_item cpi with(nolock)  ON cpi.id=cpp.f_check_item_id and cpp.rn = 1
            where cpi.f_create_time >= '{yearDate}' + '-01-01 00:00:00' and cpi.f_create_time <= '{yearDate}' + '-12-31 23:59:59' and cpi.f_state != '作废' and cpi.f_filialeid in {f_filialeid}
            ) t1 ON ui.f_userinfo_id = t1.f_userinfoid
           left join t_check_plan cpl ON t1.f_plan_id = cpl.id
where ui.f_filialeid  in {f_filialeid} and ui.f_user_state = '正常' and uf.f_table_state = '正常' and uad.f_residential_area = '{f_residential_area}' and {condition}
order by ui.f_userinfo_code
