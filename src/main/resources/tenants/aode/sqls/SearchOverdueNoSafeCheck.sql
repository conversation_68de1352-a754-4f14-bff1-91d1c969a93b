--超期未安检查询
select
    ui.f_userinfo_id,
    ui.f_userinfo_code,
    ui.f_user_name,
    ui.f_user_phone,
    ui.f_orgname,
    ui.f_filialeid,
    uf.f_meternumber,
    ua.f_address,
    uf.f_user_type,
    uf.f_table_state,
    case when t2.f_defect_content is null then '正常'
         when t2.f_defect_content like '%正常%' then '正常'
         when t2.f_defect_content like '%有隐患%' then '有隐患' end as f_defect_content,
    isnull(t2.f_offsite_time,ui.f_createfile_date) f_offsite_time, --最后一次安检时间
    isnull(t2.f_safecheck_type,'开户前安检') f_safecheck_type--最后一次安检计划类型
from
    t_userinfo ui WITH (nolock)
LEFT JOIN  t_user_address ua WITH (nolock)
ON ui.f_userinfo_id = ua.f_userinfo_id
    LEFT JOIN t_userfiles uf WITH (nolock)
ON uf.f_userinfo_id=ui.f_userinfo_id
    LEFT JOIN (
-- 取最后一次入户的安检记录
    SELECT * FROM (
    SELECT f_userinfoid,f_entry_status,f_offsite_time,f_defect_content,f_safecheck_type,
    ROW_NUMBER()OVER(partition by f_userinfoid order by f_offsite_time DESC) rownum
    FROM t_check_paper WITH (nolock) WHERE f_entry_status = '入户' AND f_filialeid in {f_filialeid_id}
    ) tt2  where tt2.rownum = 1
    ) t2 ON t2.f_userinfoid = ui.f_userinfo_id
WHERE
    ui.f_user_state='正常' AND ui.f_filialeid in {f_filialeid_id} AND
    (
-- 当前时间和目标时间相差多少天
    DATEDIFF(dd,isnull(t2.f_offsite_time,ui.f_createfile_date),GETDATE())>={lastCheckDateStart}
  AND
-- 当前时间和目标时间相差多少天
    DATEDIFF(dd,isnull(t2.f_offsite_time,ui.f_createfile_date),GETDATE())<={lastCheckDateEnd}
    )
  AND  {condition}
ORDER BY t2.f_defect_content DESC
