SELECT
    yhlevel,
    f_item_name,
    COUNT ( 0 ) AS COUNT,-- 维修总数
	SUM ( zxzgcl ) AS zxcl,-- 自行处理
	SUM ( yzwxcl ) AS yzwxcl,-- 已经转维修处理
	SUM ( wxycl ) AS wxycl,-- 维修已经处理
	SUM ( xzwxcl ) AS wcl,-- 需转维修 未处理
	SUM ( zxccl ) AS xccl,-- 现场处理
	CONVERT (
		NUMERIC ( 5, 2 ),
		(
			CONVERT ( FLOAT, SUM ( zxzgcl ) ) + CONVERT ( FLOAT, SUM ( yzwxcl ) ) + CONVERT ( FLOAT, SUM ( wxycl ) ) + CONVERT ( FLOAT, SUM ( zxccl ) )
			) * ( CONVERT ( FLOAT, 100 ) ) / ( CASE WHEN CONVERT ( FLOAT, COUNT ( 0 ) ) = 0 THEN 1 ELSE CONVERT ( FLOAT, COUNT ( 0 ) ) END )
		) AS cll --处理率

FROM
    (
    SELECT
    CASE

    WHEN
    f_defect_level LIKE '一级%' THEN
    '一级隐患'
    WHEN f_defect_level LIKE '二级%' THEN
    '二级隐患'
    WHEN f_defect_level LIKE '三级%' THEN
    '三级隐患'
    WHEN f_item_name LIKE '%异常差值' THEN
    '异常差值' ELSE '未定级隐患'
    END AS yhlevel,
    f_item_name,
    CASE

    WHEN f_user_changes = '是' THEN
    1 ELSE 0
    END AS zxzgcl,
    CASE

    WHEN f_is_repaired = '已转维修' THEN
    1 ELSE 0
    END AS yzwxcl,
    CASE

    WHEN f_is_repaired = '维修已处理' THEN
    1 ELSE 0
    END AS wxycl,
    CASE

    WHEN f_is_repaired = '需转维修' THEN
    1 ELSE 0
    END AS xzwxcl,
    CASE

    WHEN f_live_dispose = '是' THEN
    1 ELSE 0
    END AS zxccl
    FROM
    t_paper_hiddens tph WITH ( nolock )
    LEFT JOIN t_check_paper tcp WITH ( nolock ) ON tph.f_paper_id = tcp.id
    WHERE
    tph.f_create_time>= '{startDate} 00:00:00'
    AND tph.f_create_time<= '{endDate} 23:59:59'
    AND tcp.f_filialeid IN { f_filialeid }
    AND { condition }
    ) tt
GROUP BY
    yhlevel,
    f_item_name
ORDER BY
    yhlevel,
    f_item_name
