WITH handplan_check as (
    SELECT
        *, ROW_NUMBER ( ) OVER ( partition BY f_userinfo_id,MONTH(f_hand_date) ORDER BY f_hand_date DESC ) AS rn
    FROM
        t_check_handplan with(nolock)
    WHERE  f_meter_read_type = '月度抄表' AND f_user_type= '非民用' AND  YEAR(f_hand_date) = '{yearDate}' and f_filialeids in {f_filialeids} and f_userinfo_id in {f_userinfo_ids}
    and f_state = '有效'
)
SELECT
    *,
    -- 期间用气量1（1、无换表情况：用本期标况累计减去上期标况累计；2、存在换表情况：用新表底数减去新表起码加上旧表底数减去上期标况累计）
    cast(isnull(f_flow_standard_total,0) as float) - cast(isnull(f_last_flow_standard_total1,0) as float) as  gas_consumption1,
    -- 期间用气量2（用上期抄表剩余加上期间充值量减去本期剩余量）
    CAST( ((CAST(f_last_surplus_gas1 as FLOAT) + CAST(sumpregas as FLOAT)) - CAST(f_surplus_gas as float)) as decimal(38,2)) as  gas_consumption2,
    -- 期间用气量3（远传）（本期标况累计减去上期标况累计）
    CAST((CAST(f_pile_gas_volume as float) - CAST(f_last_pile_gas_volume as FLOAT)) as decimal(38,2) ) as  gas_consumption3,
    -- 差值1  （期间用气量1减去期间用气量2）
    CAST(( (cast(isnull(f_flow_standard_total,0) as float) - cast(isnull(f_last_flow_standard_total1,0) as float)) - ((CAST(f_last_surplus_gas1 as FLOAT) + CAST(sumpregas as FLOAT)) - CAST(f_surplus_gas as float))) as decimal(38,2)) d_value1,
    -- 差值2  （期间用气量1减去期间用气量3）
     CAST(((cast(isnull(f_flow_standard_total,0) as float) - cast(isnull(f_last_flow_standard_total1,0) as float)) - ((CAST(f_pile_gas_volume as float) - CAST(f_last_pile_gas_volume as FLOAT)))) as decimal(38,2) ) d_value2
from (
         SELECT
             tt1.mon,
             tt1.f_userinfo_code,
             tt1.f_userinfo_id,
             -- 取当前人的最后一条(按月取)
             ISNULL(chp.f_flow_standard_total, 0) f_flow_standard_total, -- 本期标况累计  (抄表)
             ISNULL(chp.f_surplus_gas,0) f_surplus_gas, -- 本期抄表剩余  (抄表)
             chp.f_hand_date, -- 本期抄表时间  (抄表)
             chp.f_flow_standard_instant, -- 本期标况瞬时  (抄表)
             chp.f_flow_temperature, -- 本期温度  (抄表)
             chp.f_flow_pressure, -- 本期压力  (抄表)
             CAST( ISNULL(chp.f_total_apply_gas, 0) as FLOAT) f_total_apply_gas , -- 本期流量计累计使用底数
		-- 上期取最后一条上月的数据 （如果本月有上月每月则上期为NULL）
             isnull(tt2.f_flow_standard_total,0) f_last_flow_standard_total1, -- 上期标况累计 (抄表)
             ISNULL(tt2.f_surplus_gas, 0) f_last_surplus_gas1, -- 上期表剩余 (抄表)
             tt2.f_hand_date  f_last_f_hand_date1, -- 上期抄表时间 (抄表)
             tt2.f_flow_standard_instant f_last_flow_standard_instant, -- 上期标况瞬时  (抄表)
             tt2.f_flow_temperature f_last_flow_temperature, -- 上期温度  (抄表)
             tt2.f_flow_pressure f_last_flow_pressure, -- 上期压力  (抄表)
             CAST( ISNULL(tt2.f_total_apply_gas, 0) as FLOAT) f_last_total_apply_gas , -- 上期流量计累计使用底数
             isnull((SELECT case when chp.f_meter_classify = '物联网表' then SUM ( f_collection ) else SUM ( f_pregas ) end sumpregas
                     FROM t_sellinggas WITH ( nolock )
                     WHERE f_userinfo_id = chp.f_userinfo_id AND f_operate_date >=  tt2.f_hand_date and f_operate_date <=  chp.f_hand_date
                     GROUP BY f_userinfo_id),0)  sumpregas, -- 期间购气量或购气金额
             rsp.f_last_pile_gas_volume,-- 上期标况累计（远传）
             rsp.f_last_residual_gas_volume,-- 上期表剩余 （远传）
             rsp.f_last_upload_date,-- 上期上报时间 （远传）
             rsp.f_last_isabn,-- 上期数据是否是异常数据（远传） （0,1） 0是正常 1是异常 3是空
             rsp.f_pile_gas_volume,-- 本期标况累计 （远传）
             rsp.f_residual_gas_volume,-- 本期剩余气量 （远传）
             rsp.f_upload_date,-- 本期上报日期 （远传）
             rsp.f_isabn-- 本期数据是否是异常数据 （0,1）（远传）  0是正常 1是异常 3是空
         from
                 (
                 -- 按月和人分组查每个人每个月抄表情况
                     SELECT f_userinfo_id,f_userinfo_code,month(f_hand_date) mon from t_check_handplan  WITH ( nolock )
                     WHERE  f_meter_read_type = '月度抄表' AND f_user_type= '非民用' AND  YEAR(f_hand_date) = '{yearDate}' and f_state = '有效'
                     and f_filialeids in {f_filialeids} and f_userinfo_id in {f_userinfo_ids}
                     GROUP BY f_userinfo_id,f_userinfo_code,month(f_hand_date)
                 )  tt1
                 LEFT JOIN  (
                     -- 每个月每个人的本期数据
                     select *,( select max(ft1.f_hand_date) maxhand from handplan_check ft1
                                -- 取本月数据的月份去取小于本月月份的最后一次抄表记录的月份
                                WHERE month(ft1.f_hand_date) < month(ft2.f_hand_date) and ft1.f_userinfo_id = ft2.f_userinfo_id
                              ) maxhand -- 当前时间之前的最后一个时间月份
                     from handplan_check ft2
                 ) chp ON tt1.f_userinfo_id = chp.f_userinfo_id and chp.rn = 1 and tt1.mon = month(chp.f_hand_date) -- 匹配当前月份
                 LEFT JOIN
                      -- 每个月每个人的上期数据 （上期数据取月份小于本期数据月份的最后一条数据）
                     handplan_check tt2 ON chp.f_userinfo_id = tt2.f_userinfo_id and tt2.f_hand_date = chp.maxhand
                 LEFT JOIN (
                     -- 取远传抄表系统 每个人每月最后一条数据
                     SELECT *,ROW_NUMBER() OVER (partition BY f_userinfo_code,MONTH(f_upload_date) ORDER BY f_upload_date desc ) AS rr
                     FROM t_remote_system_pilegas  WITH ( nolock )  WHERE YEAR(f_upload_date) = '{yearDate}' and f_userinfo_code in {f_userinfo_codes}
                ) rsp ON chp.f_userinfo_code = rsp.f_userinfo_code and MONTH(chp.f_hand_date) = MONTH(rsp.f_upload_date) and rr = 1
) tab
