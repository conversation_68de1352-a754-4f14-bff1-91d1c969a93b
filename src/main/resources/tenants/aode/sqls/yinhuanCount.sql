SELECT f_order_man AS f_order_man,
	servciesum,
	wanchengsum,
	wcyichangsum,
	weiwanchengsum,
	CAST (
		CONVERT ( DECIMAL ( 18, 2 ), ( wanchengsum / CAST ( servciesum AS FLOAT ) ) * 100 ) AS VARCHAR ( 20 )
	) + '%' AS wanchenglv,
	jihuakaihu,
	shijikaihu,
	leijikaihu,
	CAST (
		CONVERT ( DECIMAL ( 18, 2 ), ( shijikaihu / CAST ( jihuakaihu AS FLOAT ) ) * 100 ) AS VARCHAR ( 20 )
	) + '%' AS kaihulv
FROM
	(
	SELECT
		f_order_man,
		SUM ( servciesum ) AS servciesum,
		SUM ( wanchengsum ) AS wanchengsum,
		SUM ( wcyichangsum ) AS wcyichangsum,
		SUM ( weiwanchengsum ) AS weiwanchengsum,
		SUM ( jihuakaihu ) AS jihuakaihu,
		SUM ( shijikaihu ) AS shijikaihu,
		SUM ( leijikaihu ) AS leijikaihu
	FROM
		(
		SELECT
			f_order_man,
			COUNT ( * ) AS servciesum,
			0 wanchengsum,
			0 wcyichangsum,
			0 weiwanchengsum,
			0 jihuakai<PERSON>,
			0 shi<PERSON>kai<PERSON>,
			0 leijikaihu
		FROM
			(
			SELECT
				S.f_order_man,
				S.f_service_id,
				S.f_created_date,
				S.f_filiale_id,
				SA.*
			FROM
				t_service S
				LEFT JOIN ( SELECT * FROM ACTIVITYINS act WHERE act.id = ( SELECT MAX ( id ) FROM ACTIVITYINS WHERE PROCESSID = act.PROCESSID ) ) SA ON s.F_PROCESSID = sa.PROCESSID
			) tb
		WHERE
			tb.f_order_man IS NOT NULL
			AND { condition }
		GROUP BY
			f_order_man UNION ALL
		SELECT
			f_order_man,
			0 servciesum,
			COUNT ( * ) wanchengsum,
			0 wcyichangsum,
			0 weiwanchengsum,
			0 jihuakaihu,
			0 shijikaihu,
			0 leijikaihu
		FROM
			(
			SELECT
				S.f_order_man,
				S.f_service_id,
				S.f_created_date,
				S.f_filiale_id,
				SA.*
			FROM
				t_service S
				LEFT JOIN ( SELECT * FROM ACTIVITYINS act WHERE act.id = ( SELECT MAX ( id ) FROM ACTIVITYINS WHERE PROCESSID = act.PROCESSID ) ) SA ON s.F_PROCESSID = sa.PROCESSID
			) tb
		WHERE
			tb.defname = '维修完成'
			AND tb.f_order_man IS NOT NULL
			AND { condition }
		GROUP BY
			f_order_man UNION ALL
		SELECT
			f_order_man,
			0 servciesum,
			0 wanchengsum,
			COUNT ( * ) wcyichangsum,
			0 weiwanchengsum,
			0 jihuakaihu,
			0 shijikaihu,
			0 leijikaihu
		FROM
			(
			SELECT
				S.f_order_man,
				S.f_service_id,
				S.f_created_date,
				S.f_filiale_id,
				SA.*
			FROM
				t_service S
				LEFT JOIN ( SELECT * FROM ACTIVITYINS act WHERE act.id = ( SELECT MAX ( id ) FROM ACTIVITYINS WHERE PROCESSID = act.PROCESSID ) ) SA ON s.F_PROCESSID = sa.PROCESSID
			) tb
		WHERE
			tb.defname = '维修完成'
			AND tb.f_order_man IS NOT NULL
			AND { condition }
		GROUP BY
			f_order_man UNION ALL
		SELECT
			f_order_man,
			0 servciesum,
			0 wanchengsum,
			0 wcyichangsum,
			COUNT ( * ) weiwanchengsum,
			0 jihuakaihu,
			0 shijikaihu,
			0 leijikaihu
		FROM
			(
			SELECT
				S.f_order_man,
				S.f_service_id,
				S.f_created_date,
				S.f_filiale_id,
				SA.*
			FROM
				t_service S
				LEFT JOIN ( SELECT * FROM ACTIVITYINS act WHERE act.id = ( SELECT MAX ( id ) FROM ACTIVITYINS WHERE PROCESSID = act.PROCESSID ) ) SA ON s.F_PROCESSID = sa.PROCESSID
			) tb
		WHERE
			tb.defname <> '维修完成'
			AND tb.f_order_man IS NOT NULL
			AND { condition }
		GROUP BY
			f_order_man UNION ALL
		SELECT
			f_order_man,
			0 servciesum,
			0 wanchengsum,
			0 wcyichangsum,
			0 weiwanchengsum,
			COUNT ( * ) jihuakaihu,
			0 shijikaihu,
			0 leijikaihu
		FROM
			(
			SELECT
				tb.f_addressid,
				tb.f_order_man
			FROM
				t_service tb
				LEFT JOIN ( SELECT * FROM ACTIVITYINS act WHERE act.id = ( SELECT MAX ( id ) FROM ACTIVITYINS WHERE PROCESSID = act.PROCESSID ) ) SA ON tb.F_PROCESSID = sa.PROCESSID
			WHERE
				f_order_man IS NOT NULL
				AND { condition }
			GROUP BY
				f_order_man,
				f_addressid
			) tb1
		GROUP BY
			f_order_man UNION ALL
		SELECT
			f_order_man,
			0 servciesum,
			0 wanchengsum,
			0 wcyichangsum,
			0 weiwanchengsum,
			0 jihuakaihu,
			COUNT ( * ) shijikaihu,
			0 leijikaihu
		FROM
			(
			SELECT
				S.f_order_man,
				S.f_service_id,
				S.f_created_date,
				S.f_filiale_id,
				SA.*
			FROM
				t_service S
				LEFT JOIN ( SELECT * FROM ACTIVITYINS act WHERE act.id = ( SELECT MAX ( id ) FROM ACTIVITYINS WHERE PROCESSID = act.PROCESSID ) ) SA ON s.F_PROCESSID = sa.PROCESSID
			) tb
		WHERE
			tb.defname = '维修完成'
			AND tb.f_order_man IS NOT NULL
			AND { condition }
		GROUP BY
			f_order_man UNION ALL
		SELECT
			f_order_man,
			0 servciesum,
			0 wanchengsum,
			0 wcyichangsum,
			0 weiwanchengsum,
			0 jihuakaihu,
			0 shijikaihu,
			COUNT ( * ) leijikaihu
		FROM
			(
			SELECT
				S.f_order_man,
				S.f_service_id,
				S.f_created_date,
				S.f_filiale_id,
				SA.*
			FROM
				t_service S
				LEFT JOIN ( SELECT * FROM ACTIVITYINS act WHERE act.id = ( SELECT MAX ( id ) FROM ACTIVITYINS WHERE PROCESSID = act.PROCESSID ) ) SA ON s.F_PROCESSID = sa.PROCESSID
			) tb
		WHERE
			tb.defname = '维修完成'
			AND tb.f_order_man IS NOT NULL
			AND { condition }
		GROUP BY
			f_order_man
		) a
	GROUP BY
	f_order_man
	) b
