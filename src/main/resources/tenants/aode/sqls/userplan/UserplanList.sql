select
tcpi.f_plan_id,
tcpi.f_userinfo_code userinfocode,
tcp.f_send_operator,
tcpi.f_state,
tcp.f_issue_time,
tcpi.f_last_check_date,
tcpi.f_last_check_state,
tcp.f_checker,
cp.id,
cp.f_check_type,
cp.f_collection_type,
cp.f_entry_status
from t_check_plan tcp WITH(nolock)
left join t_check_plan_item tcpi WITH(nolock) on tcp.id=tcpi.f_plan_id
left join t_check_paper cp WITH(nolock) on cp.f_check_item_id=tcpi.id
where {condition} and tcpi.f_state !='作废'

