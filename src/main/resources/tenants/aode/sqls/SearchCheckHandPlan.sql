-- 查询安检抄表记录

select *,
       CASE
           WHEN f_user_type = '民用' THEN
               (
                       '累购气量(' + CAST( isnull( f_total_gas_total_fee, 0 ) AS VARCHAR )  + ') - 本次抄表底数(' + CAST ( isnull( f_tablebase, 0 ) AS VARCHAR ) + ') - 本期剩余气量(' + CAST ( isnull( f_surplus_gas, 0 ) AS VARCHAR ) + ') = 差值('+ cast(isnull(f_difference_value,0) as varchar) +') >' + '民用异常差值允许误差'
              ) ELSE (
               '已用气量(' +CAST ( isnull( f_flow_gas_consumption, 0 ) AS VARCHAR ) + ') - 扣减量(' + CAST ( isnull( f_flow_deduction, 0 ) AS VARCHAR ) + ') = 差值('+  cast(isnull(f_difference_value,0) as varchar)  + ') > ' + '非民用异常差值允许误差'
           )
           END abnormal_calculation
from (SELECT *,row_number() over(partition by f_userinfo_id order by f_hand_date desc) rn from t_check_handplan WITH (nolock) ) tt1
where {condition}
ORDER BY
    f_hand_date DESC
