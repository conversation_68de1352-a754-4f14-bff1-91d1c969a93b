SELECT
    ui.f_userinfo_id,
    tcp.f_plan_name,
    ui.f_createfile_date,
    ui.f_userinfo_code,
    ua.f_residential_area,
    ua.f_address,
    ui.f_user_name,
    ui.f_user_phone,
    uf.f_user_type,
    cp.f_entry_status f_last_check_state,
    cp.f_defect_content f_last_check_result,
    ui.f_comments,
    uf.f_open_date,
    ui.version,
    ui.f_filialeid,
    cp.f_checker_id,
    f_checker_name,
    cp.f_offsite_time f_last_check_date,
    cp.f_check_version,
    send_num,
    count_item,
    reshuiqi,
    bigualu
FROM
    t_userinfo ui WITH ( nolock )
	left join t_userfiles uf WITH ( nolock ) on ui.f_userinfo_id=uf.f_userinfo_id and uf.f_table_state='正常'
    left join t_user_address ua WITH ( nolock ) on ui.f_userinfo_id=ua.f_userinfo_id
    LEFT JOIN (
    select * from (
    SELECT
    id,
    f_check_plan_id,
    f_check_version,
    f_bigualu as bigualu,
    f_reshuiqi as reshuiqi,
    f_checker_name,
    f_defect_content,
    f_checker_id,
    f_userinfoid,
    f_offsite_time,
    f_check_item_id,
    f_onsite_time,
    f_entry_status,
    f_safecheck_type,
    ROW_NUMBER ( ) OVER ( partition BY f_userinfoid ORDER BY f_offsite_time DESC ) AS rn
    FROM
    t_check_paper WITH ( nolock )
    where f_entry_status != '结案' and {papercondition}
    ) u where u.rn=1
    ) cp ON ui.f_userinfo_id= cp.f_userinfoid
    LEFT JOIN t_check_plan tcp WITH ( nolock ) ON cp.f_check_plan_id= tcp.id
    LEFT JOIN ( SELECT case when COUNT ( * ) is null then 0 else  COUNT ( * ) end send_num, f_userinfoid FROM t_check_plan_item WITH ( nolock ) WHERE { itemcondition } GROUP BY f_userinfoid ) send ON ui.f_userinfo_id= send.f_userinfoid
    LEFT JOIN ( SELECT case when COUNT ( * ) is null then 0 else  COUNT ( * ) end count_item, f_userinfoid FROM t_check_paper WITH ( nolock ) WHERE { papercondition } GROUP BY f_userinfoid ) cou ON ui.f_userinfo_id= cou.f_userinfoid
WHERE
    { condition}
order by f_userinfo_code
