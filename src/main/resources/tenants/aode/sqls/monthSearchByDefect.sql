select * from  (
  select count(*) monthdefect,pd.f_device_type,SUBSTRING(tp.f_offsite_time,0,8) month from  t_devices_items di
        left join
        t_paper_devices pd on di.f_device_id = pd.id
        left join
        (select *,ROW_NUMBER() over(partition by f_checker_name,f_userinfoid order by f_offsite_time)as lastcheck
        from (select * from t_check_paper )t
        ) tp
        on tp.id = pd.f_paper_id
        where di.f_is_defect = 'true' and SUBSTRING(tp.f_offsite_time,0,8) = SUBSTRING(CONVERT(varchar,GETDATE(),23),0,8) and tp.lastcheck=1
        group by pd.f_device_type,SUBSTRING(tp.f_offsite_time,0,8)
) t
