SELECT
    uf.f_user_id As id, --表档案id
    f_position AS f_weizhi, --安装位置
    gb.f_manufacturers AS f_maker, --气表厂家
    gm.f_meter_style AS f_diaphgram_size, --气表型号
    gb.f_meter_brand AS f_aliasname, --气表品牌
    gb.f_meter_type AS f_meter_type, --气表类型
    f_meter_base AS f_prior_reading, --气表底数
    f_meternumber AS f_meter_no, --表号
    f_aroundmeter AS f_flow_direction, --左右表
    f_metertitles As f_metertitles, --表封号
    f_meter_base As  f_metergasnums, --气表底数
    uf.f_initial_base AS f_initial_base, --表基数
    uf.f_total_fee, --累购金额
    case when uf.f_meter_classify='机表' then uf.f_meter_base else uf.f_total_gas end f_total_gas,--累购气量
    gb.f_collection_type, --购气类型(按金额，按气量)
    case when uf.f_meter_classify='机表' then uf.f_meter_base  else uf.f_history_fee end f_history_fee, --户累购金额,
    case when uf.f_meter_classify='机表' then uf.f_meter_base  else uf.f_history_gas end f_history_gas, --户累购气量
    uf.f_balance_amount, -- 物联网表剩余金额
    uf.f_meter_base, --物联网表字轮数
    uf.f_airintake_seal_no, --进气口铅封号
    uf.f_airouttake_seal_no, --出气口铅封号
    version

FROM
    t_userfiles_ts uf
        LEFT JOIN t_gasbrand gb ON uf.f_gasbrand_id = gb.id LEFT JOIN
    t_gasmodel gm on
            uf.f_gasmodel_id = gm.id
WHERE
        uf.f_userinfo_id = '{f_userid}'  and (uf.f_table_state='正常' or uf.f_table_state='停用')
