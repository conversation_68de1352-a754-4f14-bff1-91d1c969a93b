select
ui.f_userinfo_id,
ui.version,
ui.f_userinfo_code,
ui.f_user_state,
ui.f_balance,
ui.f_cost_type,
ui.f_user_name,
ua.f_area,
ua.f_slice_area,
ua.f_street,
ua.f_residential_area,
ua.f_unit,
ua.f_building,
ua.f_floor,
ua.f_room,
ui.f_credentials,
ui.f_idnumber,
ui.f_user_phone,
ui.f_rent_phone,
-- ui.f_adjustable_id
ui.f_people_num,
uf.f_price_id,
uf.f_user_type,
uf.f_gasproperties,
ui.f_open_id,
ui.f_createfile_date,
ui.f_contract_id,
ui.f_taxpayer_id,
ui.f_address_phone,
ui.f_paper_name,
ui.f_paper_account,
ui.f_paper_type,
ui.f_comments,
ui.f_operator,
ui.f_last_check_date,
ui.f_last_check_state,
-- ui.f_filiale,
-- ui.f_outlets,
--ui.f_orgstr
ui.f_filialeid,
--ui.f_filialeids
ui.f_process_id,
-- ui.f_userinfo_balance_gas,
ui.f_address_str,
-- ui.f_room_id,
ua.id f_useraddress_id,
ui.f_issued_time,
-- ui.f_usercode,
-- ui.f_area_code,
ui.f_last_check_result,
-- ui.f_gasproperties2,
-- ui.f_last_sellinggas_date,
ui.f_createfile_person,
--ui.f_metertype,
--ui.f_ic_meter,
--ui.f_meterselltype,
ua.f_address,
-- ui.f_address_detail,
ui.f_orgname,
ui.f_depid,
ui.f_depname,
ui.f_operatorid,
-- ui.f_user_inhome_time
-- ui.f_user_check_time
-- ui.f_user_spare_phone
-- ui.f_user_family_remarks
-- ui.f_user_equipment_remarks
ui.f_olduserinfo_code
--ui.f_checker,
--ui.f_ventilation_state,

from
t_userfiles_ts uf
		left join t_userinfo_ts ui ON uf.f_userinfo_id = ui.f_userinfo_id
LEFT JOIN t_user_address_ts ua ON ui.f_userinfo_id = ua.f_userinfo_id
where uf.f_userinfo_id = '{id}'
