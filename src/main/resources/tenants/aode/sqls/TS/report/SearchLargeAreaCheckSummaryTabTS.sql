-- 获取每个分公司的人数，分公司ID和名字，每个分公司的对应月份的安检数
SELECT u1.Mon,                                          --每个分公司都对应12个月份
       u1.useinfoSum,                                   -- 每个公司截止到去年年末的应检数
       u1.f_filialeid,                                  -- 每个分公司的id
       u1.orgname,                                      --每个分公司的名称

       ISNULL(u2.checkCount, 0)    checkCount,          --每个分公司对应月份的已检数
       CONCAT(
               ( -- 每月安检入户率
                   CAST(ISNULL(((CAST(ISNULL(u2.checkCount, 0) AS int) * 0.1) /
                                (CAST(ISNULL(u1.useinfoSum, 1) AS int) * 0.1)), 0) * 100 AS numeric(18, 2))
                   ), '%')         checkCompletion,     -- 每月的入户率

       ----------------------------------------------------------------------
       ISNULL(u3.checkSumCount, 0) checkSumCount,       --每个分公司的本年度总入户安检数
       CONCAT(
               ( -- 年度入户率
                   CAST(ISNULL(((CAST(ISNULL(u3.checkSumCount, 0) AS int) * 0.1) /
                                (CAST(ISNULL(u1.useinfoSum, 1) AS int) * 0.1)), 0) * 100 AS numeric(18, 2))
                   ), '%')         checkSumCompletion,  -- 年度入户率
       ----------------------------------------------------------------------
       ISNULL(u4.noVisitSum, 0)    noVisitSum,          --每个公司本年度的总到访不遇数
       CONCAT(
               ( -- 年度到访不遇率
                   CAST(ISNULL(((CAST(ISNULL(u4.noVisitSum, 0) AS int) * 0.1) /
                                (CAST(ISNULL(u1.useinfoSum, 1) AS int) * 0.1)), 0) * 100 AS numeric(18, 2))
                   ), '%')         noVisitSumCompletion -- 年度到访不遇率
FROM (SELECT tt3.Mon  Mon,
             tt1.useinfoSum,  --各个分公司的用户总数
             tt1.f_filialeid, -- 各个分公司的ID
             tt2.name orgname -- 各个分公司名称
      FROM (
               -- 按档案中的分公司ID分组 统计各个分公司截止到去年年末的所有用户（应安检总数）
               SELECT COUNT(1) useinfoSum, ui.f_filialeid
               FROM t_userinfo_ts ui WITH (nolock) LEFT JOIN t_userfiles_ts uf
               WITH (nolock)
               ON ui.f_userinfo_id = uf.f_userinfo_id
               WHERE ui.f_user_state = '正常' { f_check_type!= $$: $AND uf.f_user_type = '{f_check_type}' $
                   , $ $ }
                 AND uf.f_open_date <= (SELECT dateadd(ms
                   , -3
                   , DATEADD(yy
                   , DATEDIFF(yy
                   , 0
                   , '{yearDate}-01-01 00:00:00')
                   , 0)))
                 AND uf.f_table_state = '正常'
                 AND ui.f_filialeid in {f_filialeid_id}
               GROUP BY ui.f_filialeid) tt1
               -- 关联组织表获取组织名称,
               LEFT JOIN t_organization tt2 WITH (nolock)
      ON tt1.f_filialeid = tt2.id
          -- 给各个分公司添加时间
          FULL JOIN (
          SELECT * FROM (
          SELECT 1 as Mon UNION ALL
          SELECT 2 as Mon UNION ALL
          SELECT 3 as Mon UNION ALL
          SELECT 4 as Mon UNION ALL
          SELECT 5 as Mon UNION ALL
          SELECT 6 as Mon UNION ALL
          SELECT 7 as Mon UNION ALL
          SELECT 8 as Mon UNION ALL
          SELECT 9 as Mon UNION ALL
          SELECT 10 as Mon UNION ALL
          SELECT 11 as Mon UNION ALL
          SELECT 12 as Mon
          ) am
          ) tt3 ON 1=1) u1
         FULL JOIN (
    -- 按公司和月分组
    SELECT MONTH (f_upload_date) Mon, COUNT (1) checkCount, f_filialeid
    FROM t_check_paper_ts
    WITH (nolock)
        --查询本年记录【时间需要更改】 已入户 并且类型是年度普检的记录
    WHERE f_upload_date >= '{yearDate}-01-01 00:00:00'
      and f_upload_date <= '{yearDate}-12-31 23:59:59'
      AND f_entry_status = '入户'
      AND f_safecheck_type = '{f_safecheck_type}'
      AND f_filialeid in {f_filialeid_id} { f_check_type!= $$: $AND f_check_type = '{f_check_type}' $
        , $ $ }
    GROUP BY
        MONTH (f_upload_date), f_filialeid -- 按月和分公司分组
) u2 ON u1.Mon = u2.Mon AND u1.f_filialeid = u2.f_filialeid
         FULL JOIN (SELECT f_filialeid, SUM(1) checkSumCount
                    FROM (
                             -- 根据结果表中的f_check_item_id分组并根据上传时间排序，取最后一次上传时间的状态，进行按月统计,因到访不遇计划，重新派发三次后本次才算结束
                             SELECT *, row_number() over(partition by MONTH(f_upload_date),f_userinfoid order by f_upload_date desc) rn
                             FROM t_check_paper_ts WITH (nolock)
                             WHERE f_upload_date >= '{yearDate}-01-01 00:00:00'
                               and f_upload_date <= '{yearDate}-12-31 23:59:59'
                               AND f_entry_status = '入户'
                               AND f_safecheck_type = '{f_safecheck_type}'
                               AND f_filialeid in {f_filialeid_id}
                               AND f_check_item_id is not null
                                 { f_check_type!= $$: $AND f_check_type = '{f_check_type}' $
                                 , $ $ }) tt2
                    where tt2.rn = 1
                    GROUP BY f_filialeid) u3 ON u1.f_filialeid = u3.f_filialeid
         FULL JOIN (SELECT f_filialeid, SUM(1) noVisitSum
                    FROM (
                             -- 根据结果表中的f_check_item_id分组并根据上传时间排序，取最后一次上传时间的状态，进行按月统计,因到访不遇计划，重新派发三次后本次才算结束
                             SELECT *, row_number() over(partition by MONTH(f_upload_date),f_userinfoid order by f_upload_date desc) rn
                             FROM t_check_paper_ts WITH (nolock)
                             WHERE f_upload_date >= '{yearDate}-01-01 00:00:00'
                               and f_upload_date <= '{yearDate}-12-31 23:59:59'
                               AND f_entry_status = '到访不遇'
                               AND f_safecheck_type = '{f_safecheck_type}'
                               AND f_check_item_id is not null
                               AND f_filialeid in {f_filialeid_id} { f_check_type!= $$: $AND f_check_type = '{f_check_type}' $
                                 , $ $ }) tt2
                    where tt2.rn = 1
                    GROUP BY f_filialeid) u4 ON u1.f_filialeid = u4.f_filialeid
ORDER BY u1.f_filialeid, u1.Mon ASC
