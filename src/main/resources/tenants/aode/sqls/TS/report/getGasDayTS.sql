SELECT ui.f_user_name,
       ui.f_userinfo_code,
       gc.f_address,
       gc.f_meter_classify,
       gc.hisgas,
       gc.totalgas,
       gc.f_offsite_time,
       gc.lasttime,
       gc.used,
       gc.lastused,
       gc.remain,
       gc.lastremain,
       gc.amounttotalgas2,
       gc.amountusedgas2,
       gc.aa,
       gc.bigualu,
       gc.reshuiqi,
       gc.amountday,
       case
           when lastgasdate = '' then 0
           else
               DATEDIFF(day, lastgasdate, '{f_total_date}') end    lastgasdate,
       case
           when f_offsite_time = '' then 0
           else
               DATEDIFF(day, f_offsite_time, '{f_total_date}') end laskcheckdate
FROM t_userinfo_ts ui WITH(nolock)
	INNER JOIN  t_userfiles_ts uf WITH (nolock) on ui.f_userinfo_id = uf.f_userinfo_id and uf.f_table_state='正常'
    LEFT JOIN t_gasCount_ts gc WITH (nolock) ON ui.f_userinfo_id = gc.f_userinfoid
where { condition }
ORDER BY f_upload_date desc
