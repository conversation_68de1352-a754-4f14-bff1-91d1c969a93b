-- 安检率综合汇总，按公司查询派工户数，全年应检数，正常入户户数	到访不遇户数	拒检户数	派工率	入户率
SELECT tt1.Mon,
       ISNULL(tt2.dispatchCount, 0) dispatchCount,--每月派工数
       ISNULL(tt3.checkCount, 0)    checkCount, --每月入户数
       CONCAT(
               (
                   CAST(((CAST(ISNULL(tt3.checkCount, 0) AS int) * 0.1) /
                         (CAST(ISNULL(tt2.dispatchCount, 1) AS int) * 0.1)) * 100 AS numeric(18, 2))
                   ), '%')          checkCompletion, -- 每月的入户完成率（根据派工数）

       ISNULL(tt3.noVisitCount, 0)  noVisitCount,--每月到访不遇数
       ISNULL(tt3.rejectsCount, 0)  rejectsCount, --每月拒检数
       ISNULL(tt4.check_bv,'0.00%') sumUserinfo_BV,  --全年应检总数
       ISNULL(tt4.yj,0) yj,  --全年已检总数
       ISNULL(tt4.wj,0) wj,  --全年未检总数
       ISNULL(tt4.sumUserinfo, 0)   sumUserinfo --全年应检总数

FROM (SELECT 1 as Mon
      UNION ALL
      SELECT 2 as Mon
      UNION ALL
      SELECT 3 as Mon
      UNION ALL
      SELECT 4 as Mon
      UNION ALL
      SELECT 5 as Mon
      UNION ALL
      SELECT 6 as Mon
      UNION ALL
      SELECT 7 as Mon
      UNION ALL
      SELECT 8 as Mon
      UNION ALL
      SELECT 9 as Mon
      UNION ALL
      SELECT 10 as Mon
      UNION ALL
      SELECT 11 as Mon
      UNION ALL
      SELECT 12 as Mon) tt1
         FULL JOIN
     (
-- 指定公司指定时间段 每月的派发安检用户数
         SELECT MONTH (f_create_time) Mon, COUNT (1) dispatchCount
         FROM t_check_plan_item_ts
         WITH (nolock)
         WHERE f_create_time >= '{yearDate}-01-01 00:00:00'
           and f_create_time <= '{yearDate}-12-31 23:59:59'
           AND f_filialeid in {f_filialeid_id}
           AND (f_safecheck_type = '年度普检'
            OR f_safecheck_type = '专项计划')
           AND (f_user_type = '民用'
            OR f_user_type = '非民用')
         GROUP BY MONTH (f_create_time) -- 按月分组
     ) tt2 ON tt1.Mon = tt2.Mon
         FULL JOIN (SELECT Mon,
                           sum(a) checkCount,
                           sum(b) rejectsCount,
                           sum(c) noVisitCount
                    from (select MONTH (f_upload_date) Mon, case when f_entry_status = '入户' then 1 else 0 end a, case when f_entry_status = '拒检' then 1 else 0 end b, case when f_entry_status = '到访不遇' then 1 else 0 end c
                          FROM (
                              SELECT * FROM (
                              -- 根据结果表中的f_check_item_id分组并根据上传时间排序，取最后一次上传时间的状态，进行按月统计,因到访不遇计划，重新派发三次后本次才算结束
                              SELECT *, row_number() over(partition by MONTH (f_upload_date), f_userinfoid order by f_upload_date desc) rn FROM t_check_paper_ts WITH (nolock)
                              WHERE f_upload_date >= '{yearDate}-01-01 00:00:00' and f_upload_date <= '{yearDate}-12-31 23:59:59'
                              AND (f_safecheck_type = '年度普检' OR f_safecheck_type = '专项计划') AND
                              (f_check_type = '民用' OR f_check_type = '非民用')
                              AND f_filialeid in {f_filialeid_id} AND f_check_item_id is not null
                              ) tt2 where tt2.rn = 1
                              ) t1) temp
                    GROUP BY Mon) tt3 ON tt1.Mon = tt3.Mon
         FULL JOIN
     (
         SELECT
             sumUserinfo,
             isnull(yj,0) yj,
             isnull(wj,0) wj,
             case when yj is null then '0.00%' else (CONCAT ((CAST ((( CAST ( ISNULL( yj, 0 ) AS INT ) * 0.1 ) / ( CAST ( ISNULL( sumUserinfo, 1 ) AS INT ) * 0.1 )) * 100 AS NUMERIC ( 18, 2 ))),'%')) end AS check_bv
         FROM
             (
                 SELECT
                     COUNT ( 1 ) sumUserinfo,--应检总户数
                     SUM ( CASE WHEN tcp.count_num > 0 THEN 1 ELSE 0 END ) AS yj,-- 已检查总户数
                     SUM ( CASE WHEN tcp.count_num IS NULL THEN 1 ELSE 0 END ) AS wj -- 未检查总户数
                 FROM
                     t_userinfo_ts ui WITH ( nolock )
		                LEFT JOIN t_userfiles_ts uf WITH ( nolock ) ON ui.f_userinfo_id = uf.f_userinfo_id
                        LEFT JOIN (
                         SELECT
                            count(0) as count_num,
                            f_userinfoid
                         FROM
                            t_check_paper_ts WITH ( nolock )
                         WHERE
                             f_upload_date >= '{yearDate}-01-01 00:00:00'
                             AND f_upload_date <= '{yearDate}-12-31 23:59:59'
                             AND ( f_safecheck_type = '年度普检' OR f_safecheck_type = '专项计划' )
                             AND f_check_type = '民用'
                             AND f_filialeid IN {f_filialeid_id}
                             AND f_entry_status = '入户'
                         group by f_userinfoid
                     ) tcp ON ui.f_userinfo_id = tcp.f_userinfoid
                 WHERE
                     ui.f_filialeid IN {f_filialeid_id}
                   AND ui.f_user_state = '正常'
                   AND uf.f_user_type= '民用'
                   AND uf.f_table_state = '正常'
                   AND YEAR ( uf.f_open_date ) < {yearDate}
             ) a
     ) tt4 ON 1=1

ORDER BY tt1.Mon



