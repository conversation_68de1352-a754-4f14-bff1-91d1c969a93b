SELECT
	*
FROM
	(
	SELECT
		ui.f_userinfo_id,
		ui.f_createfile_date,
		ui.f_userinfo_code,
		ua.f_street,
		ua.f_residential_area,
		ua.f_building,
		ua.f_unit,
		ua.f_floor,
		ua.f_room,
		ua.f_address,
		uf.f_adjustable_id,
		ui.f_user_name,
		ui.f_user_phone,
		uf.f_user_type,
        cp.f_entry_status f_last_check_state,
        cp.f_defect_content f_last_check_result,
        ui.f_comments,
		ui.version,
		ui.f_filialeid,
		f_checker_id,
		f_checker_name,
        cp.f_upload_date f_last_check_date,
        send_num,
        count_item
		--DATEDIFF({timetype}, isnull(isnull(ua.f_last_check_date,f_gas_date),f_input_date),GETDATE()) f_time,
		--(select count(id) from t_check_paper where f_userinfoid = ui.f_userinfo_id) count_item,
        --(select numb from  (select count(*) numb,f_userinfoid from t_check_plan_item  group by f_userinfoid) ttt where f_userinfoid = ui.f_userinfo_id  ) send_num
	FROM
		t_userfiles uf WITH(nolock)
		inner JOIN t_user_address ua WITH(nolock) ON ua.id = uf.f_useraddress_id
		inner join t_userinfo ui WITH(nolock) ON uf.f_userinfo_id = ui.f_userinfo_id
		left join (
            SELECT * FROM (
                select id cpid,f_checker_name,f_defect_content,f_checker_id,f_userinfoid,f_upload_date,f_check_item_id,f_onsite_time,f_entry_status,f_safecheck_type,ROW_NUMBER() over(partition by f_check_item_id order by f_upload_date desc) AS rn from t_check_paper WITH(nolock)
            ) AS u WHERE u.rn=1 and f_safecheck_type='年度普检' {starttime!=$$:$ and f_upload_date>'{starttime}'$,$$} {endtime!=$$:$and f_upload_date<'{endtime}'$,$$}
        ) cp on cp.f_userinfoid=ui.f_userinfo_id
        left join(
            SELECT COUNT ( * ) send_num, f_userinfoid FROM t_check_plan_item WITH(nolock) GROUP BY f_userinfoid
            )  ttt  on ttt.f_userinfoid=cp.f_userinfoid
        left join(
            SELECT COUNT ( * ) count_item, f_userinfoid FROM t_check_paper WITH(nolock) GROUP BY f_userinfoid
        )  ddd  on ddd.f_userinfoid=cp.f_userinfoid
        WHERE
		ui.f_user_state  = '正常'
		and (uf.f_table_state='正常' or uf.f_table_state='停用')
	) a
WHERE
	{condition}
ORDER BY
	{orderitem}
