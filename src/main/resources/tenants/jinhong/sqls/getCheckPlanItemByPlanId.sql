select ti.*,isnull(ti.f_check_version,0) check_version from t_check_plan_item ti WITH ( nolock )
    join t_user_address ua WITH ( nolock ) on ti.f_userinfoid=ua.f_userinfo_id
    where ti.f_plan_id = '{id}' and ti.f_state != '作废'
     and (f_upload_state = '未传' or (ti.f_last_check_state = '到访不遇' and f_upload_state = '已传' and ti.f_check_version in (1)) OR ( ti.f_last_check_state = '拒检' AND ti.f_upload_state = '已传' ))
   order by ti.f_userinfoid
