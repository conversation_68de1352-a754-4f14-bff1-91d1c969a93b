<cfg>
    <!-- SQL文件注册 -->
    <!-- 预约计划-->
    <sql alias='根据安检员查询用户档案' path='FetchUserFileByOrgChecker.sql' />
    <!--临时存放安检情况汇总-->
    <sql alias="getCheckPlanAreaList" path="getCheckPlanAreaList.sql"/>
    <!-- 查找安检单-->
    <sql alias='查找安检单' path='QueryCheckPaper.sql' />
    <!-- 手机端获取计划-->
    <sql alias='getCheckPlanMeterByPlanId' path='getCheckPlanMeterByPlanId.sql' />
    <sql alias='getCheckPlanItemByPlanId' path='getCheckPlanItemByPlanId.sql' />
</cfg>
