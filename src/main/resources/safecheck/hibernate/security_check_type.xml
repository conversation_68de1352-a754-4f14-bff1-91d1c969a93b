<?xml version="1.0"  encoding='utf-8'?>
<!DOCTYPE hibernate-mapping PUBLIC
        "-//Hibernate/Hibernate Mapping DTD 3.0//EN"
        "http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd">
<hibernate-mapping>
    <!--计划项-->
    <class entity-name="t_security_check_type" table="t_security_check_type">
        <id name="id" type="int">
            <generator class="native">
                <param name="sequence">seq_security_check_type</param>
            </generator>
        </id>
        <!--安检类型 -->
        <property name="f_safecheck_type" type="string"/>
        <!--用户类型 -->
        <property name="f_user_type" type="string"/>
        <!--版本号 -->
        <property name="version" type="int">
            <column name="version" default="0"/>
        </property>
        <!-- 安检单内容json -->
        <property name="f_json" type="clob"/>
        <!-- 操作人 -->
        <property name="f_inputtor" type="string"/>
        <!-- 操作时间 -->
        <property name="f_update_datetime" type="string"/>
        <!--所属公司id -->
        <property name="f_orgid" type="string"/>
        <!--所属公司 -->
        <property name="f_orgs" type="string"/>
    </class>
</hibernate-mapping>
