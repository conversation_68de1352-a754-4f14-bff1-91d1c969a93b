log.info("SaveSecurityCheckType接收参数: {data}"),
// t_security_check_type表版本 ++, t_security_check_type_history表 新增一条记录
ent = entity.getById("t_security_check_type", data.get("id")),

ent == null : (
    throw "数据不存在"
),null,

version = 1,
ent.version != null : (
    version = ent.version + 1
), null,

param = {
    f_security_check_type_id: ent.id,
    version: version,
    f_json: data.f_json,
    f_inputtor: data.f_inputtor,
    f_update_datetime: data.f_update_datetime
},
log.info("param123: {param}"),
result = entity.partialSave("t_security_check_type_history", param),
result = entity.partialSave("t_security_check_type", param.put("id", data.get("id"))).id,

result
