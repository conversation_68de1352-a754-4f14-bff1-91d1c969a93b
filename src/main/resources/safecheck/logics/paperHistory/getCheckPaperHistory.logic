log.info("条件是：{data.condition}"),
result = sql.querySQL("getCheckPaperHistory","
   select
       cp.f_upload_date,
       cp.f_entry_status,
       cl.f_plan_name,
       cp.id,
       {ENV.dbType == "sqlserver":"isnull(di.con,0)","nvl(di.con,0)"} defect_count,
       {ENV.dbType == "sqlserver":"isnull(di.repair_con,0)","nvl(di.repair_con,0)"} repair_con
   from t_v4_check_paper cp
   left join t_v4_check_plan cl on cp.f_plan_id=cl.id
   left join (
        select f_paper_id,
            count(1) con,
            SUM(
                CASE
                    WHEN f_is_repaired = '已处理'
                        THEN 1
                    ELSE 0
                    END
            ) as repair_con
        from t_v4_defect_items
        group by f_paper_id
   ) di on di.f_paper_id=cp.id
   where cp.f_plan_id=cl.id and {data.condition}
"),
result
