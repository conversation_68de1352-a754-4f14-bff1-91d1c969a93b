log.info("铅封保存前或者修改数据: {data}"),
log.info("铅封修改后的数据: {data.form}"),
data.form.has("id") : (
    parm = {
        f_seare_id: data.form.id,
        f_state: "0",
        f_content: "",
        f_creation_name: "{data.form.user.name}"
    },
    sealData = sql.query("getSeareData",{id:data.form.id}),
    sealData.length() > 0 : (
              charu = "wu",
              padtat = [],
              sealData[0].f_address != data.form.f_address : (
              charu = "you",
              f_address = "对地址进行了修改将{sealData[0].f_address}修改成了{data.form.f_address}---",
              padtat.put(f_address)
              ),null,
              null,
              sealData[0].f_devices_no != data.form.f_devices_no : (
              charu = "you",
              f_devices_no = "对设备编号进行了修改将{sealData[0].f_address}修改成了{data.form.f_address}---",
              padtat.put(f_devices_no)

              ),null,
              null,
              sealData[0].f_filiale != data.form.f_filiale : (
              charu = "you",
              f_filiale = "对所属公司进行了修改将{sealData[0].f_address}修改成了{data.form.f_address}---",
              padtat.put(f_filiale)

              ),null,
              null,
              sealData[0].f_frame != data.form.f_frame : (
              charu = "you",
              f_frame = "对坐标进行了修改将{sealData[0].f_address}修改成了{data.form.f_address}---",
              padtat.put(f_frame)

              ),null,
              null,
              sealData[0].f_operate_date != data.form.f_operate_date : (
              charu = "you",
              f_operate_date = "对施封时间进行了修改将{sealData[0].f_address}修改成了{data.form.f_address}----",
              padtat.put(f_operate_date)

              ),null,
              null,
              sealData[0].f_operate_id != data.form.f_operate_id : (
              charu = "you",
              f_operate_id = "对施封人员ID进行了修改将{sealData[0].f_address}修改成了{data.form.f_address}---",
              padtat.put(f_operate_id)
              ),null,
              null,
              sealData[0].f_operate_name != data.form.f_operate_name : (
              charu = "you",
              f_operate_name = "对施封人员进行了修改将{sealData[0].f_address}修改成了{data.form.f_address}---",
              padtat.put(f_operate_name)

              ),null,
              null,
              sealData[0].f_state != data.form.f_state : (
              charu = "you",
              f_state = "对铅封状态进行了修改将{sealData[0].f_address}修改成了{data.form.f_address}---",
              padtat.put(f_state)
              ),null,
              null,
              sealData[0].f_uid != data.form.f_state : (
              charu = "you",
              f_uid = "对UID进行了修改将{sealData[0].f_address}修改成了{data.form.f_address}---",
              padtat.put(f_uid)
              ),null,
              null,
              sealData[0].f_userinfo_code != data.form.f_state : (
              charu = "you",
              f_userinfo_code = "对用户编号进行了修改将{sealData[0].f_userinfo_code}修改成了{data.form.f_userinfo_code}--",
              padtat.put(f_userinfo_code)
              ),null,
              charu == "you" : (
                parm.f_content = padtat,
                entity.partialSave("t_sealchange_record", parm)
              ),null
    ),null

),(
      data.form.f_creation_name = "{data.form.user.name}"
),data
