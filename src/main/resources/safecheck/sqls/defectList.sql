select * from (
                  select
                      cp.f_user_name,
                      cp.f_userinfoid as f_userinfo_id,
                      uf.f_meternumber,
                      cp.f_address,
                      cp.f_phone_newest,
                      cp.f_offsite_time,
                      case when di.f_item_name in ('其他隐患', '其它隐患') then di.f_item_value else di.f_item_name end as f_item_name,
                      case when di.f_is_repaired is null then '未整改'
                           when di.f_is_repaired = '已处理' then '已整改'
                           else di.f_is_repaired end as f_is_repaired,
                      vp.f_phone || ' ' ||vp.f_visit_date as f_visit_info,
                      di.f_path,
                      di.f_p1_path,
                      di.f_p2_path,
                      di.f_repair_path,
                      di.f_repair1_path,
                      di.f_repair2_path,
                      di.f_repair_date,
                      case when di.f_is_repaired = '已告知' then '' else di.f_handle_date end as f_handle_date,
                      case when di.f_is_repaired = '已告知' then '' when gb.f_meter_type = '物联网表' then '关阀' else '停用' end as f_measure,
                      cp.f_checker_name,
                      di.f_repairman,
                      di.f_defect_level,
                      cp.f_filialeid,
                      cp.f_check_type,
                      u.parentid
                  from
                      t_check_paper cp
                          left join t_paper_devices pd on cp.id = pd.f_paper_id
                          left join t_devices_items di on pd.id = di.f_device_id
                          left join t_userfiles uf on cp.f_userinfoid=uf.f_userinfo_id
                          left join t_safe_visit vp on vp.f_paper_id = cp.id
                          left join t_gasbrand gb  on uf.f_gasbrand_id = gb.id
                          left join t_user u on cp.f_checker_id = u.id
                  where di.f_is_defect = 'true'
              ) t where {condition}
order by {orderitem}
