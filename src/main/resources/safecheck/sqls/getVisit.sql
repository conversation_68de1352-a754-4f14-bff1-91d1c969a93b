SELECT
    sv.*,
    cp.f_user_name,
    cp.f_address,
    cp.f_defect_text,
    tf.f_filename as  f_img_url
FROM
    t_safe_visit sv
        LEFT JOIN t_check_paper cp ON sv.f_paper_id = cp.id
        LEFT JOIN (
        SELECT
            tf.F_BLOBID,
            tf.f_filename,
            ROW_NUMBER() OVER (PARTITION BY tf.F_BLOBID ORDER BY tf.id ASC) AS rn
        FROM
            t_files tf
    ) tf ON sv.f_paper_id = tf.F_BLOBID AND tf.rn = 1
WHERE {condition}
ORDER BY sv.f_visit_date DESC
