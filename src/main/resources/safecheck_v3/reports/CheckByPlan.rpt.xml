<report>
  <sql name="getBodyData" sql="checkByPlanData" param="{condition:condition}" />
  <column number="0" width="100" startx="0" />
  <column number="1" width="80" startx="100" />
  <column number="2" width="80" startx="180" />
  <column number="3" width="80" startx="260" />
  <column number="4" width="80" startx="340" />
  <column number="5" width="80" startx="420" />
  <row number="0" height="30" starty="0" />
  <row number="1" height="30" starty="30" />
  <row number="2" height="30" starty="60" />
  <reportblock row="0" column="0" rowspan="3" columnspan="6" content="" css="" width="500" height="90">
    <headblock row="0" column="0" rowspan="1" columnspan="6" content="" css="" width="500" height="30" name="">
      <cell row="0" column="0" rowspan="1" columnspan="1" content="$安检员$" css="tdcenter report-primary" width="100" height="30" />
      <cell row="0" column="1" rowspan="1" columnspan="1" content="$计划总数$" css="tdcenter report-primary" width="80" height="30" />
      <cell row="0" column="2" rowspan="1" columnspan="1" content="$计划中未入户$" css="tdcenter report-primary" width="80" height="30" />
      <cell row="0" column="3" rowspan="1" columnspan="1" content="$计划中已入户$" css="tdcenter report-primary" width="80" height="30" />
      <cell row="0" column="4" rowspan="1" columnspan="1" content="$存在隐患户数$" css="tdcenter report-primary" width="80" height="30" />
      <cell row="0" column="5" rowspan="1" columnspan="1" content="$安检率$" css="tdcenter report-primary" width="80" height="30" />
    </headblock>
    <leftblock row="1" column="0" rowspan="2" columnspan="1" content="" css="" width="100" height="60" name="">
      <leftblock row="1" column="0" rowspan="1" columnspan="1" content="getBodyData" css="" width="100" height="30" name="left">
        <head row="1" column="0" rowspan="1" columnspan="1" content="left.f_data0" css="tdcenter report-primary" width="100" height="30" />
      </leftblock>
      <cell row="2" column="0" rowspan="1" columnspan="1" content="$合计$" css="tdcenter report-primary" width="100" height="30" />
    </leftblock>
    <bodyblock row="1" column="1" rowspan="2" columnspan="5" content="" css="" width="400" height="60" headexpression="" leftexpression="" headname="" leftname="">
      <bodyblock row="1" column="1" rowspan="1" columnspan="5" content="" css="" width="400" height="30" headexpression="" leftexpression="getBodyData" headname="" leftname="left">
        <cell row="1" column="1" rowspan="1" columnspan="1" content="left.f_data1" css="tdcenter" width="80" height="30" />
        <cell row="1" column="2" rowspan="1" columnspan="1" content="left.f_data2" css="tdcenter" width="80" height="30" />
        <cell row="1" column="3" rowspan="1" columnspan="1" content="left.f_data3" css="tdcenter" width="80" height="30" />
        <cell row="1" column="4" rowspan="1" columnspan="1" content="left.f_data4" css="tdcenter" width="80" height="30" />
        <cell row="1" column="5" rowspan="1" columnspan="1" content="left.f_data5" css="tdcenter" width="80" height="30" />
      </bodyblock>
      <cell row="2" column="1" rowspan="1" columnspan="1" content="getBodyData.sum(row.f_data1)" css="tdcenter" width="80" height="30" />
      <cell row="2" column="2" rowspan="1" columnspan="1" content="getBodyData.sum(row.f_data2)" css="tdcenter" width="80" height="30" />
      <cell row="2" column="3" rowspan="1" columnspan="1" content="getBodyData.sum(row.f_data3)" css="tdcenter" width="80" height="30" />
      <cell row="2" column="4" rowspan="1" columnspan="1" content="getBodyData.sum(row.f_data4)" css="tdcenter" width="80" height="30" />
      <cell row="2" column="5" rowspan="1" columnspan="1" content="" css="tdcenter" width="80" height="30" />
    </bodyblock>
  </reportblock>
</report>