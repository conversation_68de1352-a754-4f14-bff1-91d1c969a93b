<report>
    <sql name="SafeHiddenDisposal" sql="SafeHiddenDisposal" param="{startDate:startDate,endDate:endDate,f_subcompany:f_subcompany,department:department}" />
    <sql name="getDeviceType" sql="getDeviceType" param="{startDate:startDate,endDate:endDate,f_subcompany:f_subcompany,department:department}" />
    <column number="0" width="100" startx="0" />
    <column number="1" width="100" startx="100" />
    <column number="2" width="100" startx="200" />
    <column number="3" width="100" startx="300" />
    <column number="4" width="100" startx="400" />
    <column number="5" width="100" startx="500" />
    <column number="6" width="100" startx="600" />
    <column number="7" width="100" startx="700" />
    <row number="0" height="30" starty="0" />
    <row number="1" height="30" starty="30" />
    <row number="2" height="0" starty="60" />
    <row number="3" height="0" starty="60" />
    <reportblock row="0" column="0" rowspan="4" columnspan="8" content="" css="" width="800" height="60">
        <headblock row="0" column="0" rowspan="1" columnspan="8" content="" css="" width="800" height="30" name="">
            <cell row="0" column="0" rowspan="1" columnspan="1" content="$隐患类别$" css="tdcenter report-head head-font" width="100" height="30" />
            <cell row="0" column="1" rowspan="1" columnspan="1" content="$隐患明细$" css="tdcenter report-head head-font" width="100" height="30" />
            <cell row="0" column="2" rowspan="1" columnspan="1" content="$数量$" css="tdcenter report-head head-font" width="100" height="30" />
            <cell row="0" column="3" rowspan="1" columnspan="1" content="$隐患级别$" css="tdcenter report-head head-font" width="100" height="30" />
            <cell row="0" column="4" rowspan="1" columnspan="1" content="$客服部处置$" css="tdcenter report-head head-font" width="100" height="30" />
            <cell row="0" column="5" rowspan="1" columnspan="1" content="$安审部处置$" css="tdcenter report-head head-font" width="100" height="30" />
            <cell row="0" column="6" rowspan="1" columnspan="1" content="$用户自己整改$" css="tdcenter report-head head-font" width="100" height="30" />
            <cell row="0" column="7" rowspan="1" columnspan="1" content="$处置率$" css="tdcenter report-head head-font" width="100" height="30" />
        </headblock>
        <leftblock row="1" column="0" rowspan="3" columnspan="2" content="" css="" width="200" height="30" name="">
            <leftblock row="1" column="0" rowspan="2" columnspan="2" content="getDeviceType" css="" width="200" height="30" name="master">
                <head row="1" column="0" rowspan="2" columnspan="1" content="master.f_device_type" css="tdcenter report-left head-font" width="100" height="30" />
                <leftblock row="1" column="1" rowspan="1" columnspan="1" content="SafeHiddenDisposal.where(row.f_device_type==master.f_device_type)" css="" width="100" height="30" name="master1">
                    <head row="1" column="1" rowspan="1" columnspan="1" content="master1.f_item_name" css="tdcenter report-left head-font" width="100" height="30" />
                </leftblock>
                <cell row="2" column="1" rowspan="1" columnspan="1" content="" css="" width="100" height="0" />
            </leftblock>
            <cell row="3" column="0" rowspan="1" columnspan="1" content="" css="" width="100" height="0" />
            <cell row="3" column="1" rowspan="1" columnspan="1" content="" css="" width="100" height="0" />
        </leftblock>
        <bodyblock row="1" column="2" rowspan="3" columnspan="6" content="" css="" width="600" height="30" headexpression="" leftexpression="" headname="" leftname="">
            <bodyblock row="1" column="2" rowspan="2" columnspan="6" content="" css="" width="600" height="30" headexpression="" leftexpression="getDeviceType" headname="" leftname="master">
                <bodyblock row="1" column="2" rowspan="1" columnspan="6" content="" css="" width="600" height="30" headexpression="" leftexpression="SafeHiddenDisposal.where(row.f_device_type==master.f_device_type)" headname="" leftname="master1">
                    <cell row="1" column="2" rowspan="1" columnspan="1" content="master1.num" css="tdcenter report-main main-font" width="100" height="30" />
                    <cell row="1" column="3" rowspan="1" columnspan="1" content="master1.f_defect_level" css="tdcenter report-main main-font" width="100" height="30" />
                    <cell row="1" column="4" rowspan="1" columnspan="1" content="master1.maintenance_rect" css="tdcenter report-main main-font" width="100" height="30" />
                    <cell row="1" column="5" rowspan="1" columnspan="1" content="master1.check_rect" css="tdcenter report-main main-font" width="100" height="30" />
                    <cell row="1" column="6" rowspan="1" columnspan="1" content="master1.user_rect" css="tdcenter report-main main-font" width="100" height="30" />
                    <cell row="1" column="7" rowspan="1" columnspan="1" content="master1.disposal_rate" css="tdcenter report-main main-font" width="100" height="30" />
                </bodyblock>
                <cell row="2" column="2" rowspan="1" columnspan="1" content="" css="" width="100" height="0" />
                <cell row="2" column="3" rowspan="1" columnspan="1" content="" css="" width="100" height="0" />
                <cell row="2" column="4" rowspan="1" columnspan="1" content="" css="" width="100" height="0" />
                <cell row="2" column="5" rowspan="1" columnspan="1" content="" css="" width="100" height="0" />
                <cell row="2" column="6" rowspan="1" columnspan="1" content="" css="" width="100" height="0" />
                <cell row="2" column="7" rowspan="1" columnspan="1" content="" css="" width="100" height="0" />
            </bodyblock>
            <cell row="3" column="2" rowspan="1" columnspan="1" content="" css="" width="100" height="0" />
            <cell row="3" column="3" rowspan="1" columnspan="1" content="" css="" width="100" height="0" />
            <cell row="3" column="4" rowspan="1" columnspan="1" content="" css="" width="100" height="0" />
            <cell row="3" column="5" rowspan="1" columnspan="1" content="" css="" width="100" height="0" />
            <cell row="3" column="6" rowspan="1" columnspan="1" content="" css="" width="100" height="0" />
            <cell row="3" column="7" rowspan="1" columnspan="1" content="" css="" width="100" height="0" />
        </bodyblock>
    </reportblock>
</report>
