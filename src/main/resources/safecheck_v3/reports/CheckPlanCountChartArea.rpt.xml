<report>
  <sql name="getLeftNameTwo" sql="CheckerExamineByArea" param="{condition:condition}" />
  <sql name="getLeftNameOne" sql="searchGroupByArea" param="{condition:condition}" />
  <column number="0" width="100" startx="0" />
  <column number="1" width="100" startx="100" />
  <column number="2" width="100" startx="200" />
  <column number="3" width="100" startx="300" />
  <column number="4" width="100" startx="400" />
  <column number="5" width="100" startx="500" />
  <column number="6" width="100" startx="600" />
  <column number="7" width="100" startx="700" />
  <column number="8" width="100" startx="800" />
  <row number="0" height="30" starty="0" />
  <row number="1" height="30" starty="30" />
  <row number="2" height="30" starty="60" />
  <row number="3" height="30" starty="90" />
  <row number="4" height="20" starty="120" />
  <reportblock row="0" column="0" rowspan="4" columnspan="9" content="" css="" width="900" height="120">
    <headblock row="0" column="0" rowspan="2" columnspan="9" content="" css="" width="900" height="60" name="">
      <cell row="0" column="0" rowspan="2" columnspan="1" content="$小区$" css="tdcenter report-primary font-size19" width="100" height="60" />
      <cell row="0" column="1" rowspan="2" columnspan="1" content="$安检员$" css="tdcenter report-primary font-size19" width="100" height="60" />
      <cell row="0" column="2" rowspan="1" columnspan="3" content="$入户$" css="tdcenter report-primary font-size19" width="300" height="30" />
      <cell row="0" column="5" rowspan="1" columnspan="3" content="$隐患$" css="tdcenter report-primary font-size19" width="300" height="30" />
      <cell row="0" column="8" rowspan="2" columnspan="1" content="$备注$" css="tdcenter report-primary font-size19" width="100" height="60" />
      <cell row="1" column="2" rowspan="1" columnspan="1" content="$正常$" css="tdcenter report-primary font-size19" width="100" height="30" />
      <cell row="1" column="3" rowspan="1" columnspan="1" content="$隐患$" css="tdcenter report-primary font-size19" width="100" height="30" />
      <cell row="1" column="4" rowspan="1" columnspan="1" content="$小计$" css="tdcenter report-primary font-size19" width="100" height="30" />
      <cell row="1" column="5" rowspan="1" columnspan="1" content="$未整改$" css="tdcenter report-primary font-size19" width="100" height="30" />
      <cell row="1" column="6" rowspan="1" columnspan="1" content="$已整改$" css="tdcenter report-primary font-size19" width="100" height="30" />
      <cell row="1" column="7" rowspan="1" columnspan="1" content="$整改率$" css="tdcenter report-primary font-size19" width="100" height="30" />
    </headblock>
    <leftblock row="2" column="0" rowspan="2" columnspan="2" content="" css="" width="200" height="60" name="">
      <leftblock row="2" column="0" rowspan="1" columnspan="2" content="getLeftNameOne" css="" width="200" height="30" name="left1">
        <head row="2" column="0" rowspan="1" columnspan="1" content="left1.f_residential_area" css="tdcenter report-primary font-size19" width="100" height="30" />
        <leftblock row="2" column="1" rowspan="1" columnspan="1" content="getLeftNameTwo.where(row.f_residential_area==left1.f_residential_area)" css="" width="100" height="30" name="left2">
          <head row="2" column="1" rowspan="1" columnspan="1" content="left2.f_checker_name" css="tdcenter report-primary font-size19" width="100" height="30" />
        </leftblock>
      </leftblock>
      <cell row="3" column="0" rowspan="1" columnspan="2" content="$合计$" css="tdcenter report-primary font-size19" width="200" height="30" />
    </leftblock>
    <bodyblock row="2" column="2" rowspan="2" columnspan="7" content="" css="" width="700" height="60" headexpression="" leftexpression="" headname="" leftname="">
      <bodyblock row="2" column="2" rowspan="1" columnspan="7" content="" css="" width="700" height="30" headexpression="" leftexpression="getLeftNameOne" headname="" leftname="left1">
        <bodyblock row="2" column="2" rowspan="1" columnspan="3" content="" css="" width="300" height="30" headexpression="" leftexpression="getLeftNameTwo.where(row.f_residential_area==left1.f_residential_area)" headname="" leftname="left2">
          <cell row="2" column="2" rowspan="1" columnspan="1" content="left2.check_ok_num" css="tdcenter report-success" width="100" height="30" />
          <cell row="2" column="3" rowspan="1" columnspan="1" content="left2.check_no_ok_num" css="tdcenter report-success" width="100" height="30" />
          <cell row="2" column="4" rowspan="1" columnspan="1" content="left2.check_count" css="tdcenter report-success" width="100" height="30" />
        </bodyblock>
        <bodyblock row="2" column="5" rowspan="1" columnspan="4" content="" css="" width="400" height="30" headexpression="" leftexpression="getLeftNameTwo.where(row.f_residential_area==left1.f_residential_area)" headname="" leftname="left2">
          <cell row="2" column="5" rowspan="1" columnspan="1" content="left2.no_handle_num" css="tdcenter report-success" width="100" height="30" />
          <cell row="2" column="6" rowspan="1" columnspan="1" content="left2.handle_num" css="tdcenter report-success" width="100" height="30" />
          <cell row="2" column="7" rowspan="1" columnspan="1" content="left2.handle_percentage" css="tdcenter report-success" width="100" height="30" />
          <cell row="2" column="8" rowspan="1" columnspan="1" content="" css="tdcenter report-success" width="100" height="30" />
        </bodyblock>
      </bodyblock>
      <cell row="3" column="2" rowspan="1" columnspan="1" content="getLeftNameTwo.sum(row.check_ok_num)" css="tdcenter report-success" width="100" height="30" />
      <cell row="3" column="3" rowspan="1" columnspan="1" content="getLeftNameTwo.sum(row.check_no_ok_num)" css="tdcenter report-success" width="100" height="30" />
      <cell row="3" column="4" rowspan="1" columnspan="1" content="getLeftNameTwo.sum(row.check_count)" css="tdcenter report-success" width="100" height="30" />
      <cell row="3" column="5" rowspan="1" columnspan="1" content="getLeftNameTwo.sum(row.no_handle_num)" css="tdcenter report-success" width="100" height="30" />
      <cell row="3" column="6" rowspan="1" columnspan="1" content="getLeftNameTwo.sum(row.handle_num)" css="tdcenter report-success" width="100" height="30" />
      <cell row="3" column="7" rowspan="1" columnspan="1" content="" css="tdcenter report-success" width="100" height="30" />
      <cell row="3" column="8" rowspan="1" columnspan="1" content="" css="tdcenter report-success" width="100" height="30" />
    </bodyblock>
  </reportblock>
  <cell row="4" column="0" rowspan="1" columnspan="9" content="" css="" width="900" height="20" />
</report>
