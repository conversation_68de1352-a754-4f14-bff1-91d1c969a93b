--民用日现金气量核对
--month：月份
--return：日期，居民气量，实际金额，户数，用户气量单价
 select distinct f_checker, f_checker_id, plan_year_month from (
 (select   f_checker, f_checker_id,
 cast(substring(convert(varchar(20),DATEADD(YEAR,1,f_last_check_date),120), 1, 7) as varchar(30)) plan_year_month from t_userfiles
 where  DATEADD(YEAR,1,f_last_check_date)>='{f_last_check_date}-01-01'
 and  DATEADD(YEAR,1,f_last_check_date)<='{f_last_check_date}-12-31' and f_user_type!='居民'
 and f_last_check_date is not null and f_last_check_date !='null'
 and f_subcompany='{f_subcompany}' and f_state != '注销' and f_user_name is not null and f_user_name != 'null'
 group by f_checker, f_checker_id, substring(convert(varchar(20),DATEADD(YEAR,1,f_last_check_date),120), 1, 7)
 )
 union
 (
 select   f_checker, f_checker_id,
 cast(substring(convert(varchar(20),DATEADD(YEAR,2,f_last_check_date),120), 1, 7) as varchar(30)) plan_year_month from t_userfiles
 where  DATEADD(YEAR,2,f_last_check_date)>='{f_last_check_date}-01-01'
 and  DATEADD(YEAR,2,f_last_check_date)<='{f_last_check_date}-12-31' and f_user_type='居民'
 and f_last_check_date is not null and f_last_check_date !='null'
 and f_subcompany='{f_subcompany}' and f_state != '注销' and f_user_name is not null and f_user_name != 'null'
 group by f_checker, f_checker_id, substring(convert(varchar(20),DATEADD(YEAR,2,f_last_check_date),120), 1, 7)
 )
 ) t
