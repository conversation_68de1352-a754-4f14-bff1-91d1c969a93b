select f_residential_area, count(*) n, 
sum(case when f_repaired ='已修' then 1 else 0 end) checked, 
sum(case when f_state='未修' then 1 else 0 end) unchecked,
sum(case when f_state='在修' then 1 else 0 end) halfchecked,
sum(case when f_defect_urgency='A' then 1 else 0 end) na,
sum(case when f_defect_urgency='B' then 1 else 0 end) nb,
sum(case when f_defect_urgency='C' then 1 else 0 end) nc 
from t_check_paper 
where f_repairman ='{f_repairman}' and {condition} group by f_residential_area order by f_residential_area