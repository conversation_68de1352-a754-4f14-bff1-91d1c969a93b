SELECT
    tcb.*,
    isnull( num.num, 0 ) AS num,
    isnull( tc.xiafa, 0 ) AS xiafa,
    isnull( num2.num, 0 ) AS num2,
    isnull( num3.ruhu_num, 0 ) AS num3,
    num.num - num3.ruhu_num as num4
FROM
    t_check_book tcb
        LEFT JOIN (
        SELECT
            tua.f_check_book_id,
            COUNT ( 0 ) AS num
        FROM
            t_userinfo tui
                LEFT JOIN t_userfiles tuf ON tui.f_userinfo_id= tuf.f_userinfo_id
                LEFT JOIN t_user_address tua ON tuf.f_userinfo_id = tua.f_userinfo_id
                LEFT JOIN t_area ta ON tua.f_residential_area_id = ta.id
                LEFT JOIN t_check_book tcb ON tua.f_check_book_id= tcb.id
        WHERE
            tui.f_user_state != '销户'
          AND ( tuf.f_table_state= '正常' OR tuf.f_table_state= '停用' OR tuf.f_table_state= '待开通' )
        GROUP BY
            tua.f_check_book_id
    ) num ON tcb.id= num.f_check_book_id
        left join (
        SELECT
            tua.f_check_book_id,
            COUNT ( 0 ) AS xiafa
        FROM
            t_userinfo tui
                LEFT JOIN t_userfiles tuf ON tui.f_userinfo_id= tuf.f_userinfo_id
                LEFT JOIN t_user_address tua ON tuf.f_userinfo_id = tua.f_userinfo_id
                LEFT JOIN t_area ta ON tua.f_residential_area_id = ta.id
                LEFT JOIN t_check_book tcb ON tua.f_check_book_id= tcb.id
                LEFT JOIN t_check_plan tcp ON tua.f_plan_id = tcp.id
        WHERE
            tui.f_user_state != '销户'
          AND ( tuf.f_table_state= '正常' OR tuf.f_table_state= '停用' OR tuf.f_table_state= '待开通' )
          and tcp.f_plan_year = YEAR (
            GETDATE())  and  tcp.f_book_id = tcb.id
        GROUP BY
         tua.f_check_book_id
    ) tc ON tcb.id= tc.f_check_book_id
        LEFT JOIN (
        SELECT
            ta.f_check_book_id,
            COUNT ( 0 ) AS num
        FROM
            t_area ta
        where f_check_book_id is not null
        GROUP BY
            ta.f_check_book_id
    ) num2 ON tcb.id= num2.f_check_book_id
        LEFT JOIN (
        SELECT
            f_check_book_id,
            SUM ( case when ruhu_num > 0 then 1 else 0 end ) ruhu_num
        FROM
            (
                SELECT
                    tua.f_check_book_id,
                    tui.f_userinfo_id,
                    SUM ( CASE WHEN tcp.f_entry_status = '入户' THEN 1 ELSE 0 END ) AS ruhu_num
                FROM
                    t_userinfo tui
                        LEFT JOIN t_userfiles tuf ON tui.f_userinfo_id= tuf.f_userinfo_id
                        LEFT JOIN t_user_address tua ON tuf.f_userinfo_id = tua.f_userinfo_id
                        LEFT JOIN t_area ta ON tua.f_residential_area_id = ta.id
                        LEFT JOIN t_check_book tcb ON tua.f_check_book_id= tcb.id
                        LEFT JOIN t_check_paper tcp ON tui.f_userinfo_id = tcp.f_userinfoid
                WHERE
                    tui.f_user_state != '销户'
		            AND ( tuf.f_table_state= '正常' OR tuf.f_table_state= '停用' OR tuf.f_table_state= '待开通' )
                GROUP BY
                    tua.f_check_book_id,
                    tui.f_userinfo_id
            ) t
        GROUP BY
            f_check_book_id
    ) num3 ON tcb.id= num3.f_check_book_id

WHERE
    { condition }
ORDER BY
    tcb.f_create_date asc,tcb.f_check_book_name asc
