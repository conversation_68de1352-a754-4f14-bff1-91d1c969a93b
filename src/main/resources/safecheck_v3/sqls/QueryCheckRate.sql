select t0.f_subcompany ,isnull(t1.cplan,0) cplan,isnull(t0.call,0) call,isnull(t0.centry,0) centry,isnull(t0.cdeny,0) cdeny ,
isnull(t0.cabsent,0) cabsent ,isnull(t0.nouse,0) nouse,isnull(t0.total,0) dangernum,ISNULL(A,0) A ,ISNULL(B,0) B ,ISNULL(C,0) C,
isnull(t0.overduenum,0) overduenum,isnull(t0.nocabsent,0) nocabsent,
ISNULL(t0.nohandledanger,0) nohandledanger,
cast(CONVERT(decimal(18, 2),isnull(round((call/(t1.cplan*1.0))*100,2),0)) as VARCHAR) +'%' checkrate,
(case when call = 0 then  '0'  else (cast(CONVERT(decimal(18, 2),isnull(round((centry/(call*1.0))*100,2),0)) as varchar)+'%') end ) entery_rate
 from
(
  select u2.f_subcompany,
  u2.f_orgid,
  count(u1.id ) cplan,
	sum(case when u3.id is null then 0 else 1 end) call,
	sum(case when u3.f_entry_status ='入户' then 1 else 0 end) centry,
	sum(case when u3.f_entry_status ='拒检' then 1 else 0 end) cdeny,
	sum(case when u3.f_entry_status ='到访不遇' then 1 else 0 end) cabsent,
	sum(case when u3.f_entry_status ='未使用天然气' then 1 else 0 end) nouse,
	sum(case when u4.f_paper_id is null then 0 else 1 end)  total,
	count(case when u4.A is null then 0 else A end)  A,
	count(case when u4.B is null then 0 else B end)  B,
	count(case when u4.C is null then 0 else C end)  C,
	sum(case when u1.f_last_check_state='到访不遇' and u3.f_entry_status<>'到访不遇' then 1 else 0 end) nocabsent,
	sum(case when DATEDIFF(month,u1.f_last_check_date,u3.f_offsite_time)>24 and u1.f_user_type='居民' then 1
	 when DATEDIFF(month,u1.f_last_check_date,u3.f_offsite_time)>12 and u1.f_user_type='非民用' then 1
	 else 0  end) overduenum,

	0 nohandledanger
from t_check_plan_item u1 left join
t_check_plan u2 on u1.f_plan_id=u2.id
left join t_check_paper u3 on u1.id=u3.f_check_item_id
left join
(select *
from (
SELECT f_paper_id,substring(f_defect_level,1,1) f_defect_level FROM t_check_paper_defect
) as C
PIVOT(count(f_defect_level) FOR [f_defect_level] IN([A],[B],[C])) AS T ) u4 on u3.id=u4.f_paper_id
 where (CONVERT(varchar(100),( cast(u2.f_plan_year as varchar)+'-' +RIGHT('00'+cast(u2.f_plan_month as varchar),2))))>=CONVERT(varchar(100),'{startDate}' +'-01 00:00:00',14)
 and (CONVERT(varchar(100),( cast(u2.f_plan_year as varchar)+'-' +RIGHT('00'+cast(u2.f_plan_month as varchar),2))))<=CONVERT(varchar(100),'{endDate}' +'-31 23:59:59',14)
and  u3.f_offsite_time>=CONVERT(varchar(100),'{startDate}' +'-01 00:00:00',14) and u3.f_offsite_time<=CONVERT(varchar(100),'{endDate}' +'-31 23:59:59',14)
and u2.f_orgid = '{f_orgid}'
group by u2.f_orgid,u2.f_subcompany
) t0  left join (select count(id) as cplan,f_orgid from  t_check_plan_item  group by f_orgid) t1 on t0.f_orgid = t1.f_orgid  order by call desc


