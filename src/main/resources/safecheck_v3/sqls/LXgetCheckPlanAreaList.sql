--安检情况汇总
--分组项：f_plan_month计划月份，f_no_checkplan计划类型，f_residential_area小区，f_plan_name计划名称
--        f_checker_name安检员
--求和项：f_user_number档案数，f_plan_total计划总数，f_weijian未检计划数，f_yijian已检计划数
--        f_ruhu入户数，f_buyu到访不遇数，f_jujian拒检数，安检率
select
 count(*) f_plan_total,
 sum(f_weijian) f_weijian,
 sum(f_ruhu) f_yijian,
 sum(f_ruhu) f_ruhu,
 sum(f_buyu) f_buyu,
 sum(f_jujian) f_jujian,
 f_year,
 f_month,
 {ENV.dbType == "sqlserver":$cast(CONVERT(decimal(18,2),case when count(*)=0 then 0.00 else sum(f_ruhu)/(count(*)+0.00)*100.00 end)  as varchar)+'%'$, $to_char(to_number(case when count(*)=0 then 0.00 else sum(f_ruhu)/(count(0)+0.00)*100.00 end)) || '%'$} f_check_bv,
 {groupName}
from(
	select
		cpi.f_no_checkplan,
		cpi.f_residential_area,
		cpi.f_plan_id,
		cpl.f_plan_name,
		cpi.f_filialeid,
		cpl.f_plan_year as f_year,
		cpl.f_plan_month as f_month,
		{ENV.dbType == "sqlserver":$cast(cpl.f_plan_year as varchar)+'年'+cast(cpl.f_plan_month as varchar)+'月'$, $	CONCAT(CONCAT(cpl.f_plan_year,'年'),CONCAT(cpl.f_plan_month,'月'))$} f_plan_month,
		case when cpall.f_checker_name is null then cpl.f_checker else cpall.f_checker_name end f_checker_name,
		case when cpi.f_state='未检' then 1 else 0 end f_weijian,
		case when cpi.f_state='已检' then 1 else 0 end f_yijian,
		case when f_entry_status='入户' then 1 else 0 end f_ruhu,
		case when f_entry_status='到访不遇' then 1 else 0 end f_buyu,
		case when f_entry_status='拒检' then 1 else 0 end f_jujian
	from
        t_check_plan_item cpi
	left join t_check_plan cpl on cpi.f_plan_id = cpl.id
	left join
	(select
		f_check_item_id,max(f_offsite_time) f_offsite_time
	from t_check_paper
	group by f_check_item_id) cp on cpi.id=cp.f_check_item_id
	left join t_check_paper cpall on cp.f_check_item_id = cpall.f_check_item_id and cp.f_offsite_time = cpall.f_offsite_time
	where cpi.f_state!='作废'
) ch left join (
 select ua.f_residential_area area,count(*) f_user_number
 from t_user_address ua
 left join t_userinfo u on ua.f_userinfo_id=u.f_userinfo_id
 where f_user_state='正常'
 group by ua.f_residential_area
) u on ch.f_residential_area = u.area
where {condition}
group by
{groupName},f_year,f_month
 order by f_year desc , f_month desc
