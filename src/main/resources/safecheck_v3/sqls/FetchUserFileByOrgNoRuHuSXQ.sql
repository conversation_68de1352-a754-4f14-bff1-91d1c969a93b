SELECT
    *
FROM
    (
        SELECT
            ui.f_userinfo_id,
            ui.f_userinfo_code,
            ui.f_user_name,
            ua.f_street,
            building,
            ua.f_building,
            unit,
            ua.f_unit,
            ua.f_residential_area,
            floor,
            ua.f_floor,
            ua.f_room,
            room,
            ua.f_address,
            ua.f_slice_area,
            CASE
                mr.f_valveState
                WHEN '0' THEN
                    '开阀' ELSE '关阀'
                END f_valve_state,
            f_user_phone,
            ui.f_last_check_state,
            ui.f_last_check_date,
            ui.f_createfile_date,
            ua.f_last_check_result,
            ui.f_uservent_date,
            ui.f_comments,
            ui.version,
            ua.id,
            f_plan_name,
            uf.f_user_type,
            uf.f_metertitles,
            uf.f_meter_classify,
            mb.f_book_slice_area,
            mb.f_book_name
        FROM
            t_userinfo ui
            LEFT JOIN (
                SELECT
                    ( CASE PATINDEX ( '%[^0-9]%', f_building ) WHEN 0 THEN f_building ELSE 999 END ) building,
                    ( CASE PATINDEX ( '%[^0-9]%', f_unit ) WHEN 0 THEN f_unit ELSE 999 END ) unit,
                    ( CASE PATINDEX ( '%[^0-9]%', f_floor ) WHEN 0 THEN f_floor ELSE 999 END ) floor,
                    ( CASE PATINDEX ( '%[^0-9]%', f_room ) WHEN 0 THEN f_room ELSE 999 END ) room,
                    *
                FROM
                    t_user_address
            ) ua ON ui.f_userinfo_id = ua.f_userinfo_id
            LEFT JOIN t_userfiles uf ON uf.f_userinfo_id = ui.f_userinfo_id
            LEFT JOIN t_meteread mr WITH ( nolock ) ON uf.f_meteread_maxid = mr.id
            LEFT JOIN t_meter_book mb ON mb.id = uf.f_meter_book_num
            LEFT JOIN t_check_plan cp ON ua.f_plan_id = cp.id
            LEFT JOIN t_userproperties up ON ui.f_userinfo_id = up.f_userinfo_id AND up.id = ( SELECT MAX( id ) FROM t_userproperties WHERE f_state = '有效' AND f_parameter_key = 'blackList' GROUP BY f_userinfo_id )
            LEFT JOIN t_area area ON ua.F_RESIDENTIAL_AREA_ID = area.id
        WHERE
            { condition }
          AND uf.f_table_state IN ( '正常', '停用', '待开通' )
          AND ui.f_user_state != '销户' and ua.id is not null
    ) a
ORDER BY
    { orderitem }
