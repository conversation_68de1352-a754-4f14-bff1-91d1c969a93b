SELECT
    tcp.f_userinfo_code,
    tcp.id paper_id,
    tdi.id device_id,
    tpd.f_paper_id,
    tcp.f_user_name,
    tcp.f_address,
    tcp.f_user_phone,
    tcp.f_checker_name,
    tpd.f_device_type,
    tdi.f_item_name,
    tdi.f_path,
    tdi.f_p1_path,
    tdi.f_p2_path,
    tdi.f_p3_path,
    tdl.f_item_value,
    tdl.id device_list_id,
    tdl.f_create_time,
    uf.f_user_type,
    tcp.f_repair_approved_note as f_comments,
    uf.f_meternumber
FROM
    t_check_paper tcp
        LEFT JOIN t_paper_devices tpd ON tcp.id= tpd.f_paper_id
        LEFT JOIN t_devices_items tdi ON tpd.id= tdi.f_device_id
        LEFT JOIN t_devices_items_lists tdl ON tdi.id= tdl.f_item_id
        LEFT JOIN (select * from t_userfiles where f_table_state in ('正常','停用')) uf ON tcp.f_userinfoid = uf.f_userinfo_id
        LEFT JOIN t_userinfo tui ON tcp.f_userinfoid = tui.f_userinfo_id
WHERE
	{condition}
	AND tdi.f_is_defect= 'true'
	AND tdl.f_dealwith IS NULL
    AND tdi.f_deal_dispose in ('未处理','','自行整改','自行处理','现场处理')
    AND tdi.f_is_repaired IS NULL
	AND tcp.f_filialeid ='{f_filialeids}'
ORDER BY
	tdl.f_create_time DESC
