SELECT
    *
FROM
    (
        SELECT
            ui.f_userinfo_id,
            ui.f_createfile_date,
            ui.f_userinfo_code,
            ui.f_user_name,
            ui.f_user_state,
            ui.f_user_phone,
            isnull( rh, 0 ) rh1,
            isnull( df, 0 ) df1,
            isnull( jj, 0 ) jj1,
            tp1.f_entry_status,
            tp1.f_checker_name,
            CASE

                WHEN tp1.f_entry_status = '入户' THEN
                    ({ f_defect_content }) ELSE '未知'
                END AS f_last_check_result,
            tp1.f_defect_content,
            uf.f_meter_book_num,
            uf.f_user_type,
            uf.f_meternumber,
            uf.f_meter_brand,
            uf.f_meter_classify,
            uf.f_gasproperties,
            uf.f_gas_date,
            tp.f_userinfoid,
            tp1.f_onsite_time,
            tp1.f_offsite_time,
            ua.f_slice_area,
            ua.f_address,
            ua.f_street,
            ua.f_residential_area,
            ua.f_building,
            ua.f_pcd,
            ua.f_unit,
            ua.f_floor,
            ua.f_room,
            ua.building,
            ua.unit,
            ua.floor
        FROM
            t_userinfo ui
                LEFT JOIN (
                SELECT
                    ( CASE PATINDEX ( '%[^0-9]%', f_building ) WHEN 0 THEN f_building ELSE 999 END ) building,
                    ( CASE PATINDEX ( '%[^0-9]%', f_unit ) WHEN 0 THEN f_unit ELSE 999 END ) unit,
                    ( CASE PATINDEX ( '%[^0-9]%', f_floor ) WHEN 0 THEN f_floor ELSE 999 END ) floor,
                    *
                FROM
                    t_user_address
            ) ua ON ui.f_userinfo_id = ua.f_userinfo_id
                LEFT JOIN (
                SELECT
                    f_user_type,
                    f_meter_book_num,
                    f_userinfo_id,
                    f_meter_classify,
                    f_gasproperties,
                    f_meternumber,
                    f_meter_brand,
                    f_gas_date
                FROM
                    t_userfiles tu
                        LEFT JOIN t_gasbrand gb ON tu.f_gasbrand_id = gb.id
                WHERE
                        f_table_state IN ( '正常', '停用' )
            ) uf ON ua.f_userinfo_id = uf.f_userinfo_id
                LEFT JOIN (
                SELECT
                    f_userinfoid,
                    SUM( CASE WHEN f_entry_status = '入户' THEN 1 ELSE 0 END ) rh,
                    SUM( CASE WHEN f_entry_status = '到访不遇' THEN 1 ELSE 0 END ) df,
                    SUM( CASE WHEN f_entry_status = '拒检' THEN 1 ELSE 0 END ) jj
                FROM
                    t_check_paper
                WHERE
                    { condition.condition2 }
                GROUP BY
                    f_userinfoid
            ) tp ON ui.f_userinfo_id = tp.f_userinfoid
                left join (select max(f_upload_date) f_upload_date ,f_userinfoid from t_check_paper where { condition.condition2 } group by f_userinfoid) tp2 on tp2.f_userinfoid = ui.f_userinfo_id
                LEFT JOIN t_check_paper tp1 ON tp1.f_userinfoid = tp2.f_userinfoid AND tp2.f_upload_date = tp1.f_upload_date
    ) a
WHERE
    { condition.condition1 }
order by f_onsite_time desc
