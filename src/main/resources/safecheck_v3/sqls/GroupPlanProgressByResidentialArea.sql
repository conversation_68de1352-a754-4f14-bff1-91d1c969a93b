select f_residential_area, n,nouse, checked, n-checked unchecked, verified, withdrawn, (checked-verified-withdrawn) unverified, centry, cdeny, cabsent from  
(
select f_residential_area, count(*) n, 
sum(case when f_state='已检' then 1 else 0 end) checked, 
sum(case when f_state='已检' and f_upload_state='已传' then 1 else 0 end) verified, 
sum(case when f_state='已检' and f_upload_state='已传' then 0 else 1 end) withdrawn,
sum(case when f_last_check_state = '入户' then 1 else 0 end) centry,
sum(case when f_last_check_state = '拒检' then 1 else 0 end) cdeny,
sum(case when f_last_check_state = '到访不遇' then 1 else 0 end) cabsent,
sum(case when f_last_check_state = '未使用天然气' then 1 else 0 end) nouse 
from t_check_plan_item 
where f_plan_id='{f_plan_id}' and {condition} group by f_residential_area 
) t order by f_residential_area