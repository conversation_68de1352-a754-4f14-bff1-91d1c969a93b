-- 有计划安检，按计划时间分组，无计划安检按上传时间分组，统计12个月的，计划用户数、已检用户数、无计划用户数、有计划完成率
select
    tmon.Mon,
    isnull(ts.yiji<PERSON>hu,0) yiji<PERSON>hu,
    isnull(ts.ji<PERSON><PERSON><PERSON><PERSON>,0) jihuayonghushu,
    (select count(*) usercount from t_userinfo where f_open_date is not null and f_open_date != ''  and  f_open_date <= cast(('{planyear}-'+ case when tmon.Mon<10 then '0'+cast(tmon.Mon+1 as varchar) else cast(tmon.Mon as varchar) end  + '-01 00:00:00') as datetime)  and f_user_state = '正常' ) usercount,
    isnull(ts.weiji<PERSON>hu,0) weiji<PERSON>hu,
    isnull(ts.ruhushu,0) ruhushu,
    isnull(ts.junjianshu,0) junji<PERSON>hu,
    isnull(ts.daofangbuyushu,0) daofangbuyushu,
    isnull(ts.wu<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,0) wu<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
    isnull(ts.ji<PERSON><PERSON><PERSON><PERSON><PERSON>,0) + isnull(ts.wu<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,0) anjianzongshu,
    isnull(CONVERT(decimal(18,2),case when isnull(ts.jihuayonghushu,0)=0 then 0.00 else ts.yijianshu/(ts.jihuayonghushu+0.00)*100.00 end), 0.00) youjihuawanchenglv, --有计划完成率
    isnull(CONVERT(decimal(18,2),case when isnull((select count(*) usercount from t_userinfo where f_open_date is not null and f_open_date != ''  and  f_open_date <= cast(('{planyear}-'+ case when tmon.Mon<10 then '0'+cast(tmon.Mon+1 as varchar) else cast(tmon.Mon as varchar) end  + '-01 00:00:00') as datetime)  and f_user_state = '正常' ),0)=0 then 0.00 else isnull(ts.jihuayonghushu,0)/(isnull((select count(*) usercount from t_userinfo where f_open_date is not null and f_open_date != ''  and  f_open_date <= cast(('{planyear}-'+ case when tmon.Mon<10 then '0'+cast(tmon.Mon+1 as varchar) else cast(tmon.Mon as varchar) end  + '-01 00:00:00') as datetime)  and f_user_state = '正常' ),0)+0.00)*100.00 end), 0.00) yipaijihuashu
from (
         SELECT 1 as Mon UNION ALL
         SELECT 2 as Mon UNION ALL
         SELECT 3 as Mon UNION ALL
         SELECT 4 as Mon UNION ALL
         SELECT 5 as Mon UNION ALL
         SELECT 6 as Mon UNION ALL
         SELECT 7 as Mon UNION ALL
         SELECT 8 as Mon UNION ALL
         SELECT 9 as Mon UNION ALL
         SELECT 10 as Mon UNION ALL
         SELECT 11 as Mon UNION ALL
         SELECT 12 as Mon
) tmon left join (
    select
        f_plan_month,
        sum(case when f_no_checkplan = '有计划安检' then 1 else 0 end) jihuayonghushu,  -- 计划用户数 （不包含无计划）
        sum(case when f_no_checkplan = '有计划安检' and f_state = '已检' then 1 else 0 end) yijianshu, -- 已检用户数 （不包含无计划）
        sum(case when f_no_checkplan = '有计划安检' and f_state = '未检' then 1 else 0 end) weijianshu, -- 未检用户数 （不包含无计划）
        sum(case when f_no_checkplan = '无计划安检' then 1 else 0 end) wujihuayonghushu, -- 无计划用户数
        sum(case when f_state = '已检' and f_entry_status = '入户' then 1 else 0 end) ruhushu, --入户数 （包含无计划）
        sum(case when f_state = '已检' and f_entry_status = '拒检' then 1 else 0 end) junjianshu, --拒检数 （包含无计划）
        sum(case when f_state = '已检' and f_entry_status = '到访不遇' then 1 else 0 end) daofangbuyushu --到访不遇数 （包含无计划）
    from (
             select cpl.f_plan_month,cpi.f_no_checkplan,cpi.f_state,cpp.f_entry_status from t_check_plan_item cpi
                left join t_check_plan cpl  ON cpi.f_plan_id = cpl.id
                left join ( select * from (select ROW_NUMBER() over(partition by f_check_item_id order by f_upload_date desc) rn,* from t_check_paper) tt where tt.rn = 1 ) cpp  ON cpi.id = cpp.f_check_item_id
             where f_plan_year = {planyear} and cpl.f_issued = '是' and {condition}
             union all
             select MONTH(cpp.f_upload_date) f_plan_month,cpi.f_no_checkplan,cpi.f_state,cpp.f_entry_status from t_check_plan_item cpi
                left join ( select * from (select ROW_NUMBER() over(partition by f_check_item_id order by f_upload_date desc) rn,* from t_check_paper) tt where tt.rn = 1 ) cpp  ON cpi.id = cpp.f_check_item_id
             where cpi.f_no_checkplan = '无计划安检' and YEAR(cpp.f_upload_date) = {planyear} and {condition}
         ) tt
    group by f_plan_month
) ts ON tmon.Mon = ts.f_plan_month
order by tmon.Mon asc
