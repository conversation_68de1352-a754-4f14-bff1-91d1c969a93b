select
    uf.f_gasproperties,
    pd.f_device_type
from t_check_paper cp
         LEFT JOIN t_paper_devices pd on cp.id = pd.f_paper_id
         LEFT JOIN t_devices_items di on di.f_device_id = pd.id
         left join t_userfiles uf on cp.f_userinfoid=uf.f_userinfo_id
         LEFT JOIN t_user_address tud ON cp.f_userinfoid= tud.f_userinfo_id
where cp.f_offsite_time >= '{startTime1}' and cp.f_offsite_time <= '{endTime}'
  and uf.f_table_state in ('正常', '停用') and di.f_is_defect = 'true' and {condition}
GROUP BY uf.f_gasproperties, pd.f_device_type
ORDER BY uf.f_gasproperties, pd.f_device_type