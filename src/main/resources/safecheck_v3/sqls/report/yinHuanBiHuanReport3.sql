select
    f_gasproperties, f_device_type, f_item_name, f_defect_level,
    SUM(
            CASE
                WHEN f_offsite_time >= '{startTime1}' and f_offsite_time <= '{startTime2}' and f_complete = '未完成'
                    THEN 1
                ELSE 0
                END
        ) as qichu,
    SUM(
            CASE
                WHEN f_offsite_time >= '{startTime2}' and f_offsite_time <= '{endTime}'
                    THEN 1
                ELSE 0
                END
        ) as z<PERSON><PERSON><PERSON>,
    SUM(
            CASE
                WHEN f_offsite_time >= '{startTime2}' and f_offsite_time <= '{endTime}' and f_complete = '已完成'
                    THEN 1
                ELSE 0
                END
        ) as wancheng,
    SUM(
            CASE
                WHEN f_offsite_time >= '{startTime1}' and f_offsite_time <= '{endTime}' and f_complete = '未完成'
                    THEN 1
                ELSE 0
                END
        ) as qimo,
    SUM(
            CASE
                WHEN CONVERT(varchar,GETDATE(),120) > f_handle_date and f_offsite_time >= '{startTime1}' and f_offsite_time <= '{endTime}' and f_complete = '已完成'
                    THEN 1
                ELSE 0
                END
        ) as y<PERSON><PERSON><PERSON><PERSON>,
    SUM(
            CASE
                WHEN CONVERT(varchar,GETDATE(),120) > f_handle_date and f_offsite_time >= '{startTime1}' and f_offsite_time <= '{endTime}' and f_complete = '未完成'
                    THEN 1
                ELSE 0
                END
        ) as yuqiweiwancheng
from (
         select
             uf.f_gasproperties,
             pd.f_device_type,
             di.f_item_name,
             case when di.f_defect_level is null or di.f_defect_level = '' then '未分级' else di.f_defect_level end as f_defect_level,
             case when di.f_live_dispose = '是' or di.f_deal_dispose='现场整改' or di.f_repair_date is not null then '已完成' else '未完成' end as f_complete,
             di.f_handle_date,
             cp.f_offsite_time
         from t_check_paper cp
                  LEFT JOIN t_paper_devices pd on cp.id = pd.f_paper_id
                  LEFT JOIN t_devices_items di on di.f_device_id = pd.id
                  left join t_userfiles uf on cp.f_userinfoid=uf.f_userinfo_id
                  LEFT JOIN t_user_address tud ON cp.f_userinfoid= tud.f_userinfo_id
         where cp.f_offsite_time >= '{startTime1}' and cp.f_offsite_time <= '{endTime}'
           and uf.f_table_state in ('正常', '停用') and di.f_is_defect = 'true' and {condition}
     ) t
GROUP BY f_gasproperties, f_device_type, f_item_name, f_defect_level
order by f_gasproperties, f_device_type