SELECT *,
       CONVERT(decimal(18,2),case when t_defect_count=0 then 0.00 else t_defect_processed/(t_defect_count+0.00)*100.00 end) t_rectification_rate, --隐患整改率
       CONVERT(decimal(18,2),case when t_defect_count=0 then 0.00 else t_defect_processed/(t_defect_count+0.00)*100.00 end) t_user_rectification_rate --隐患整改率（户）
FROM (

SELECT
    isnull(cpp.f_checker_name,'') f_checker_name,
    case when di.f_defect_level is null or di.f_defect_level = '' then '无等级' else di.f_defect_level end f_defect_level,
    cpd.f_device_type,
    di.f_item_name,
    count(*) t_defect_count, --隐患总数
    SUM ( CASE
              WHEN  (di.f_deal_dispose in ('现场整改', '自行处理', '转维修', '报修') OR isnull(di.f_is_repaired,'未处理') = '已处理' OR di.f_audit_results = '通过')
                  THEN 1
              ELSE 0
        END ) t_defect_processed, --已处理隐患数
    SUM (CASE
             WHEN  (di.f_deal_dispose not in ('现场整改', '自行处理', '转维修', '报修') OR isnull(di.f_is_repaired,'未处理') != '已处理' OR di.f_audit_results != '通过')
                 THEN 1
             ELSE 0
        END) t_defect_untreated, --未处理隐患数
    isnull(max(y_defect_count),0) y_defect_count,
    isnull(max(y_defect_processed),0) y_defect_processed,
    isnull(max(y_defect_untreated),0) y_defect_untreated
FROM
    t_check_plan_item cpi
        LEFT JOIN t_check_plan cpl ON cpl.id = cpi.f_plan_id
        LEFT JOIN t_check_paper cpp ON cpi.id = cpp.f_check_item_id
        LEFT JOIN t_paper_devices cpd ON cpd.f_paper_id = cpp.id
        LEFT JOIN t_devices_items di ON cpd.id = di.f_device_id
        LEFT JOIN (
        select
            isnull(cpp.f_checker_name,'') f_checker_name,
            case when di.f_defect_level is null or di.f_defect_level = '' then '无等级' else di.f_defect_level end f_defect_level,
            cpd.f_device_type,
            di.f_item_name,
            count(*) y_defect_count, --隐患总数
            SUM ( CASE
                      WHEN  (di.f_deal_dispose in ('现场整改', '自行处理', '转维修', '报修') OR isnull(di.f_is_repaired,'未处理') = '已处理' OR di.f_audit_results = '通过')
                          THEN 1
                      ELSE 0
                END ) y_defect_processed, --已处理隐患数
            SUM (CASE
                     WHEN  (di.f_deal_dispose not in ('现场整改', '自行处理', '转维修', '报修') OR isnull(di.f_is_repaired,'未处理') != '已处理' OR di.f_audit_results != '通过')
                         THEN 1
                     ELSE 0
                END ) y_defect_untreated --未处理隐患数
        from  t_check_plan_item cpi
                  LEFT JOIN t_check_plan cpl ON cpl.id = cpi.f_plan_id
                  LEFT JOIN t_check_paper cpp ON cpi.id = cpp.f_check_item_id
                  LEFT JOIN t_paper_devices cpd ON cpd.f_paper_id = cpp.id
                  LEFT JOIN t_devices_items di ON cpd.id = di.f_device_id
        where di.f_is_defect = 'true' AND CPP.f_checker_name IS NOT NULL
        and {condition}
        and cpp.f_upload_date >= dateadd(year,-1,'{plan_date_start} 00:00:00')
        and  cpp.f_upload_date <= dateadd(year,-1,'{plan_date_end} 23:59:59')
        {
        f_filialeid!=$$:
        $ and cpp.f_filialeid in {f_filialeid}$,
        $$
        }
        GROUP BY
            cpp.f_checker_name,
            di.f_defect_level,
            cpd.f_device_type,
            di.f_item_name
        having count(*) > 0
    ) last_paper ON cpp.f_checker_name = last_paper.f_checker_name and
                    di.f_defect_level = last_paper.f_defect_level and cpd.f_device_type = last_paper.f_device_type and di.f_item_name = last_paper.f_item_name
where di.f_is_defect = 'true' AND CPP.f_checker_name IS NOT NULL
and {condition}
and cpp.f_upload_date >= '{plan_date_start} 00:00:00'
and  cpp.f_upload_date <= '{plan_date_end} 23:59:59'
    {
    f_filialeid!=$$:
    $ and cpp.f_filialeid in {f_filialeid}$,
    $$
    }
GROUP BY
    cpp.f_checker_name,
    di.f_defect_level,
    cpd.f_device_type,
    di.f_item_name
having count(*) > 0
) tt
order by f_checker_name desc
