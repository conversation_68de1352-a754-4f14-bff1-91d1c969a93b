SELECT
	*
FROM (
		SELECT
		pd.f_paper_id,
		pd.f_device_type,
		di.id,
		di.f_item_name,
		di.f_path,
        di.f_p1_path,
        di.f_p2_path,
        di.f_p3_path,
		di.f_audit_results,
		di.f_repair_path,
		di.f_repair1_path,
		di.f_repairman,
		di.f_repair_date,
		di.f_repair_path_source,
        dil.f_item_value,
        dil.f_dealpath,
        di.f_defect_remark,
        dil.f_other_dealpath1,
        dil.f_other_dealpath2,
		(
			CASE
				WHEN di.f_is_defect != 'true' OR di.f_is_defect IS NULL THEN '无隐患'
				WHEN di.f_is_defect = 'true' AND (di.f_user_changes = '是' or di.f_deal_dispose in ('自行处理', '自行整改')) THEN '用户自行整改'
				WHEN di.f_is_defect = 'true' AND (di.f_live_dispose = '是' or di.f_deal_dispose='现场整改') THEN '现场已处理'
				WHEN di.f_is_defect = 'true' AND di.f_is_repaired = '已处理' THEN '维修已处理'
				WHEN di.f_is_defect = 'true' AND di.f_is_repaired = '转维修' THEN '转维修'
				ELSE '隐患未处理'
			END
		) f_repaire_type,
        (
            CASE
            WHEN di.f_is_defect != 'true' OR di.f_is_defect IS NULL THEN '无隐患'
            WHEN di.f_is_defect = 'true' AND ((di.f_live_dispose = '是' or di.f_deal_dispose='现场整改') or di.f_is_repaired = '已处理')
            THEN '已处理'
            ELSE '未处理'
        END
        ) f_repaire_state,
		(
			CASE
				WHEN di.f_is_defect != 'true' THEN 1
				WHEN di.f_is_defect = 'true' AND (di.f_user_changes = '是' or di.f_deal_dispose in ('自行处理', '自行整改')) THEN 2
				WHEN di.f_is_defect = 'true' AND (di.f_live_dispose = '是' or di.f_deal_dispose='现场整改') THEN 3
				WHEN di.f_is_defect = 'true' AND di.f_is_repaired = '已处理' THEN 4
				WHEN di.f_is_defect = 'true' AND di.f_is_repaired = '转维修' THEN 5
				ELSE 10
			END
		) f_danger_leave
	FROM
		t_paper_devices pd
		LEFT JOIN t_devices_items di ON di.f_device_id = pd.id
        LEFT JOIN t_devices_items_lists dil ON dil.f_item_id= di.id
	WHERE
		pd.f_paper_id = '{f_paper_id}'
) t_temp
WHERE
	{condition}
ORDER BY f_danger_leave DESC
