SELECT LEFT
    ( f_filialeid, 2 ) AS f_filialeid,
    <PERSON><PERSON> ( mytype ) AS mytype,
    SUM ( fmytype ) AS fmytype,
    SUM ( countxq ) AS countxq
FROM
    (-- 用户类型统计（来自t_userinfo和t_userfiles的关联）
    SELECT
    o.name AS f_filialeid,
    CASE
    WHEN uf.f_user_type = '民用' THEN
    1 ELSE 0
    END AS mytype,
    CASE WHEN uf.f_user_type = '非民用' THEN
    1 ELSE 0
    END AS fmytype,
    0 AS countxq
    FROM
    t_userinfo u
    JOIN t_userfiles uf ON u.f_userinfo_id = uf.f_userinfo_id
    LEFT JOIN t_organization o ON o.id= u.f_filialeid
    WHERE
    u.f_filialeid IS NOT NULL
    AND u.f_open_date <= CONVERT ( DATETIME, '{endDate} 23:59:59' )
    { orgid != $$ :
    $
    AND u.f_filialeid IN { orgid } $,
    $ $
    }
    AND ( u.f_user_state = '正常' OR u.f_user_state = '停用' ) UNION ALL-- 区域统计（来自t_area）
    SELECT
    o.name AS f_filialeid,
    0 AS mytype,
    0 AS fmytype,
    COUNT ( * ) AS countxq
    FROM
    t_area a
    LEFT JOIN t_organization o ON o.id= a.f_filialeid
    WHERE
    a.f_operate_date <= CONVERT ( DATETIME, '{endDate} 23:59:59' )
    { orgid != $$ :
    $
    AND a.f_filialeid IN { orgid } $,
    $ $
    }
    GROUP BY
    o.name
    ) ss
GROUP BY
    f_filialeid
ORDER BY
    f_filialeid
