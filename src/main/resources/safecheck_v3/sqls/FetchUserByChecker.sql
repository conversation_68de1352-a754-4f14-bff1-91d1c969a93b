-- 预约查询用户
select * from(
    select ui.f_userinfo_id as f_userinfoid,
    ui.f_userinfo_id,
    ui.version,
    ui.f_userinfo_code,
    ui.f_user_state,
    ui.f_balance,
    ui.f_cost_type,
    ui.f_user_name,
    ua.f_slice_area,
    ua.f_street,
    ua.f_residential_area,
    ua.f_unit,
    ua.f_building,
    ua.f_floor,
    ua.f_room,
    ua.f_enter_number,
    ui.f_credentials,
    ui.f_idnumber,
    ui.f_user_phone,
    ui.f_rent_phone,
    ui.f_zuhu_phone,
    -- ui.f_adjustable_id,
    ui.f_people_num,
    uf.f_user_type,
    ui.f_open_id,
    ui.f_createfile_date,
    ui.f_contract_id,
    ui.f_taxpayer_id,
    ui.f_address_phone,
    ui.f_paper_name,
    ui.f_paper_account,
    ui.f_paper_type,
    ui.f_comments,
    ui.f_operator,
    ui.f_last_check_date,
    ui.f_last_check_state,
    --ui.f_filiale,
    --ui.f_orgstr,
    ui.f_filialeid,
    --ui.f_filialeids,
    ui.f_process_id,
    ui.f_address_str,
    ui.f_issued_time,
    ui.f_createfile_person,
    ua.f_address,
    ui.f_orgname,
    ui.f_depid,
    ui.f_depname,
    ui.f_operatorid,
    ui.f_olduserinfo_code,
    (case PATINDEX('%[^0-9]%', ua.f_building)
        when 0
            then ua.f_building
        else 999
        end
    ) build,
    (case PATINDEX('%[^0-9]%', ua.f_floor)
        when 0
            then ua.f_floor
        else 999
        end
    ) floor1,
    tcm.f_community_name,
    uf.f_meternumber
    from t_userfiles uf {ENV.dbType == "sqlserver":$ with(nolock) $,$ $}
    left join t_userinfo ui {ENV.dbType == "sqlserver":$ with(nolock) $,$ $} ON uf.f_userinfo_id = ui.f_userinfo_id
    LEFT JOIN t_user_address ua {ENV.dbType == "sqlserver":$ with(nolock) $,$ $} ON ui.f_userinfo_id = ua.f_userinfo_id
    left join t_community tcm on ua.f_community_id = tcm.id
    where {condition} AND f_user_state != '销户' AND  uf.f_table_state in ('正常','停用','待开通')
    {
      f_check_type == $应检$ && f_user_type == $$:
      $
            AND (uf.f_user_type = '民用'  and (isnull(f_last_check_date,convert(varchar(50),f_createfile_date,23)) >= substring(CONVERT(varchar,DATEADD(month,-{myCycle},Datename(year,GetDate())+'-'+Datename
    (month,GetDate())+'-'+Datename(day,GetDate())),23),0,11) and  isnull(f_last_check_date,convert(varchar(50),f_createfile_date,23)) <= substring(CONVERT(varchar,DATEADD(month,-{myCycle},Datename(year,GetDate())+'-12-31'),23),0,11)+' 23:59:59'))
        or (uf.f_user_type = '非民用' and (isnull(f_last_check_date,convert(varchar(50),f_createfile_date,23)) >= substring(CONVERT(varchar,DATEADD(month,-{fyCycle},Datename(year,GetDate())+'-'+Datename
    (month,GetDate())+'-'+Datename(day,GetDate())),23),0,11) and  isnull(f_last_check_date,convert(varchar(50),f_createfile_date,23)) <= substring(CONVERT(varchar,DATEADD(month,-{fyCycle},Datename(year,GetDate())+'-12-31'),23),0,11)+' 23:59:59'))
      $,
      f_check_type == $应检$ && f_user_type == $民用$:
      $
        AND (uf.f_user_type = '民用'  and (isnull(f_last_check_date,convert(varchar(50),f_createfile_date,23)) >= substring(CONVERT(varchar,DATEADD(month,-{myCycle},Datename(year,GetDate())+'-'+Datename
    (month,GetDate())+'-'+Datename(day,GetDate())),23),0,11) and  isnull(f_last_check_date,convert(varchar(50),f_createfile_date,23)) <= substring(CONVERT(varchar,DATEADD(month,-{myCycle},Datename(year,GetDate())+'-12-31'),23),0,11)+' 23:59:59'))
      $,
      f_check_type == $应检$ && f_user_type == $非民用$:
      $
         AND (uf.f_user_type = '非民用' and (isnull(f_last_check_date,convert(varchar(50),f_createfile_date,23)) >= substring(CONVERT(varchar,DATEADD(month,-{fyCycle},Datename(year,GetDate())+'-'+Datename
    (month,GetDate())+'-'+Datename(day,GetDate())),23),0,11) and  isnull(f_last_check_date,convert(varchar(50),f_createfile_date,23)) <= substring(CONVERT(varchar,DATEADD(month,-{fyCycle},Datename(year,GetDate())+'-12-31'),23),0,11)+' 23:59:59'))
      $,

        f_check_type == $超期$ && f_user_type == $$:
      $
            AND (uf.f_user_type = '民用'  and (isnull(f_last_check_date,convert(varchar(50),f_createfile_date,23)) < substring(CONVERT(varchar,DATEADD(month,-{myCycle},Datename(year,GetDate())+'-'+Datename
    (month,GetDate())+'-'+Datename(day,GetDate())),23),0,11)))
        or (uf.f_user_type = '非民用' and (isnull(f_last_check_date,convert(varchar(50),f_createfile_date,23)) < substring(CONVERT(varchar,DATEADD(month,-{fyCycle},Datename(year,GetDate())+'-'+Datename
    (month,GetDate())+'-'+Datename(day,GetDate())),23),0,11)))
      $,
      f_check_type == $超期$ && f_user_type == $民用$:
      $
        AND (uf.f_user_type = '民用'  and (isnull(f_last_check_date,convert(varchar(50),f_createfile_date,23)) < substring(CONVERT(varchar,DATEADD(month,-{myCycle},Datename(year,GetDate())+'-'+Datename
    (month,GetDate())+'-'+Datename(day,GetDate())),23),0,11)))
      $,
      f_check_type == $超期$ && f_user_type == $非民用$:
      $
         AND (uf.f_user_type = '非民用' and (isnull(f_last_check_date,convert(varchar(50),f_createfile_date,23)) < substring(CONVERT(varchar,DATEADD(month,-{fyCycle},Datename(year,GetDate())+'-'+Datename
    (month,GetDate())+'-'+Datename(day,GetDate())),23),0,11)))
      $,
      f_check_type == $$ && f_user_type != $$:
      $
      and uf.f_user_type = '{f_user_type}'
      $,
      $$
    }
    {
        f_sign != $$ :
        $ and f_sign = '{f_sign}' $,
        $ $
    }
    {
        f_user_state != $$ :
        $ and f_user_state = '{f_user_state}' $,
        $ $
    }
    {
        f_residential_area != $$ :
        $ and ua.f_residential_area like '%{f_residential_area}%' $,
        $ $
    }
    {
        f_user_name != $$ :
        $ and f_user_name like '%{f_user_name}%' $,
        $ $
    }
    {
        f_address != $$ :
        $ and ua.f_address like '%{f_address}%' $,
        $ $
    }
    {
        f_unit != $$ :
        $ and ua.f_unit = '{f_unit}' $,
        $ $
    }
    {
        f_building != $$ :
        $ and ua.f_building = '{f_building}' $,
        $ $
    }
    and  ui.f_filialeid = '{f_filialeid}'
    {
    groupitem !=$$:
    $,
    group by {groupitem}
    $,
    groupitem ==$$:
    $$,
    $$
    }
) t
order by {orderitem}
