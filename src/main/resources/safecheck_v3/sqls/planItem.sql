SELECT
    i.f_idnumber,
    i.f_user_phone,
    i.f_comments,
    c.f_plan_name,
    i.f_userinfoid,
    i.f_userinfo_code,
    i.f_street,
    i.f_residential_area,
    i.f_building,
    i.f_unit,
    i.f_floor,
    i.f_room,
    i.f_plan_id,
    c.id,
    isnull( c.f_checker, tt.f_checker_name ) AS f_checker,
    isnull( c.f_checker_id, tt.f_checker_id ) AS f_checker_id,
    i.f_user_name,
    i.f_address,
    i.f_state,
    i.f_last_check_date,
    i.f_last_check_state,
    i.f_last_check_result,
    i.f_user_type,
    i.id f_check_item_id,
    i.f_filialeid,
    i.f_supplement_tag,
    c.f_safecheck_type,
    i.f_newfile_date
FROM
    t_check_plan_item i
        LEFT JOIN t_check_plan c ON i.f_plan_id= c.id
        LEFT JOIN (
        SELECT
            a.f_checker_name,
            a.f_checker_id,
            a.f_check_item_id
        FROM
            t_check_paper a
                INNER JOIN ( SELECT f_check_item_id, MAX ( f_upload_date ) upload_time FROM t_check_paper GROUP BY f_check_item_id ) b ON a.f_check_item_id = b.f_check_item_id
                AND b.upload_time = a.f_upload_date
        WHERE
                F_NO_CHECKPLAN = '有计划安检'
    ) tt ON tt.f_check_item_id = i.id
WHERE
    { condition }
ORDER BY
    i.f_residential_area,
    i.f_building,
    i.f_unit,
    i.f_floor,
    i.f_room
