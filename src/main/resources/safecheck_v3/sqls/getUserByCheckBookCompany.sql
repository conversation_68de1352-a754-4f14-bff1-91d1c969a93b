SELECT
    tui.f_userinfo_id,
    tui.f_user_name,
    tui.f_userinfo_code,
    tui.f_user_phone,
    tuf.f_user_type,
    tua.f_address,
    tua.f_residential_area,
    tcb.f_check_book_name
FROM
    t_userinfo tui
        LEFT JOIN t_userfiles tuf ON tui.f_userinfo_id= tuf.f_userinfo_id
        LEFT JOIN t_user_address tua ON tuf.f_userinfo_id = tua.f_userinfo_id
        LEFT JOIN t_company tc ON tui.f_corporate_name_id = tc.id
        LEFT JOIN t_check_book tcb ON tc.f_check_book_id= tcb.id
WHERE
        tui.f_user_state = '正常'
  AND ( tuf.f_table_state= '正常' OR tuf.f_table_state= '停用' )
  AND { condition }
ORDER by tui.f_userinfo_code
