SELECT m.* FROM (SELECT tm.f_meter_no, ti.*, cp.f_plan_name, cp.f_plan_year, cp.f_plan_month, cp.f_checker_id FROM t_check_plan_item ti LEFT JOIN t_plan_item_meter tm ON tm.f_plan_item_id = ti.id LEFT JOIN t_check_plan cp ON cp.id = ti.f_plan_id WHERE {condition}) m ORDER BY f_prearranged_date,f_residential_area,CASE WHEN CAST(f_building AS INTEGER) IS NOT NULL THEN CAST(f_building AS INTEGER) ELSE 999999 END,CASE WHEN CAST(f_unit AS INTEGER) IS NOT NULL THEN CAST(f_unit AS INTEGER) ELSE 999999 END,CASE WHEN CAST(f_floor AS INTEGER) IS NOT NULL THEN CAST(f_floor AS INTEGER) ELSE 999999 END,CASE WHEN CAST(f_room AS INTEGER) IS NOT NULL THEN CAST(f_room AS INTEGER) ELSE 999999 END,f_address
