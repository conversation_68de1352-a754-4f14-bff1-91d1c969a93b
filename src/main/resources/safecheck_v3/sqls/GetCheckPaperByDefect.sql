SELECT
    *
FROM (
         SELECT
             t_temp.*,f_meter_book_num,f_slice_area,tu.f_gasproperties,tu.f_gasproperties_second,
             (
                 CASE f_defect_count
                     WHEN 0
                         THEN '无隐患'
                     ELSE '有隐患'
                     END
                 ) f_has_defect,
             (
                 CASE f_repair_count
                     WHEN 0
                         THEN '隐患已处理'
                     ELSE '有隐患'
                     END
                 ) f_is_repair
         FROM (
                  SELECT
                      cp.id,
                      cp.f_entry_status,
                      cp.f_residential_area,
                      cp.f_user_phone,
                      cp.f_userinfo_code,
                      cp.f_user_name,
                      cp.f_onsite_time,
                      cp.f_offsite_time,
                      cp.f_checker_name,
                      ua.f_address,
                      cp.f_check_plan_id,
                      cpl.f_plan_name,
                      ui.f_userinfo_id,
                      ui.f_filialeid,
                      {f_defect_content} f_defect_content,
                      cp.f_repaired,
                      (
                      SELECT
                      SUM(
                      CASE di.f_is_defect
                      WHEN 'true'
                      THEN 1
                      ELSE 0
                      END
                      ) f_defect_count
                      FROM
                      t_paper_devices pd
                      LEFT JOIN t_devices_items di ON di.f_device_id = pd.id
                      WHERE
                      pd.f_paper_id = cp.id
                      )
                      f_defect_count,
                      (
                      SELECT
                      SUM(
                      CASE
                      WHEN dl.f_dealwith is null THEN 0
                      ELSE 1
                      END
                      ) f_repair_count
                      FROM
                      t_paper_devices pd
                      LEFT JOIN t_devices_items di ON di.f_device_id = pd.id
                      LEFT JOIN t_devices_items_lists dl ON dl.f_item_id = di.id

                      WHERE
                      pd.f_paper_id = cp.id
                      )
                      f_repair_count,
                      (
                      SELECT
                      SUM(
                      CASE
                      WHEN di.f_is_repaired is null THEN 0
                      ELSE 1
                      END
                      ) f_is_repaired
                      FROM
                      t_paper_devices pd
                      LEFT JOIN t_devices_items di ON di.f_device_id = pd.id

                      WHERE
                      pd.f_paper_id = cp.id
                      )
                      f_is_repaired
                  FROM
                      t_check_paper cp
                      LEFT JOIN t_check_plan cpl ON cpl.id = cp.f_check_plan_id
                      LEFT JOIN t_userinfo ui ON ui.f_userinfo_id = cp.f_userinfoid
                      LEFT JOIN t_user_address ua ON ua.f_userinfo_id = cp.f_userinfoid
                  WHERE
                      ui.f_user_state  = '正常'
              ) t_temp
                  left join (select * from t_userfiles where f_table_state = '正常' or f_table_state = '停用') tu on t_temp.f_userinfo_id=tu.f_userinfo_id
                  left join t_user_address tud on t_temp.f_userinfo_id=tud.f_userinfo_id

     ) t_temp2
WHERE
    {condition} AND f_filialeid in {f_filialeids}
ORDER BY
    f_offsite_time
