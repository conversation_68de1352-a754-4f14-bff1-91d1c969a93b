SELECT f_device_type,
       f_item_value,
       f_defect_level,
       count(*) hiddennum,
       sum(yichuli) yichuli,
       sum(weichuli) weichuli,
       sum(zhuanweixiu) zhuanweixiu
FROM (
         SELECT tpd.f_device_type,
                isnull(tdi.f_item_name,'')+':'+ isnull(tdil.f_item_value,'')  f_item_value,
                isnull(tdi.f_defect_level,'') f_defect_level,
                case when tdi.f_is_repaired = '已处理' then 1 else 0 end yichuli,
                case when tdi.f_is_repaired is null  then 1 else 0 end weichuli,
                case when tdi.f_is_repaired = '转维修' then 1 else 0 end zhuanweixiu
         from t_check_paper tp
                  left join t_userfiles tuf on tp.f_userinfoid = tuf.f_userinfo_id and tuf.f_table_state != '换表'
                  left join t_gasbrand gb on tuf.f_gasbrand_id = gb.id
                  LEFT JOIN t_paper_devices tpd ON tp.id= tpd.f_paper_id
                  LEFT JOIN t_devices_items tdi ON tpd.id= tdi.f_device_id
                  LEFT JOIN t_devices_items_lists tdil ON tdi.id= tdil.f_item_id
                  LEFT JOIN t_userinfo tu ON tp.f_userinfoid = tu.f_userinfo_id
                  LEFT JOIN t_user usr ON tp.f_checker_id = usr.id
         WHERE 1=1
           and tdi.f_is_defect = 'true'
           and tdi.f_item_name not in ('金额异常差值', '气量异常差值')
           and {condition}
             {
             f_filialeid!=$$:
             $
           and tp.f_filialeid in {f_filialeid}$
             , $$
             }
             {
             startDate!=$$:$
           and tp.f_offsite_time>='{startDate}' $
             , $$
             }
             {
             endDate!=$$:$
           and tp.f_offsite_time<='{endDate}' $
             , $$
             }
     ) h
GROUP BY h.f_device_type, h.f_item_value, h.f_defect_level
