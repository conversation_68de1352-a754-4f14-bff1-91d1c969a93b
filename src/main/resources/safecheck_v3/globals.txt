{
    "lastNewFormDate": "1980-01-01 00:00:01",
    "f_repairman_id": "x",
    "f_user_id": "aaa",
    "cc_base_url": "http://192.168.191.1:8081/SafeCheck",
    "REMOTE_WWW_PREFIX": "C:\\test\\",
    "LOCAL_PATH_PREFIX": "/data/data/com.aofeng.hybridwork/files/",
	"REMOTE_BZ_PREFIX": "D:\GIT\Safecheck\\SafeCheck\\src\\main\\resources\\",
    "ProjectNativeMethod": "com.aofeng.hybridwork.android.ProjectNativeMethod",
	"pos": "true",
    "device": "com.aofeng.hybrid.pos.device.ZTDevice",
    "cloud_url":"http://61.134.61.238:8006/",
    "printer": "com.aofeng.hybrid.pos.printer.BTPrint",
    "reader":"com.aofeng.hybrid.pos.reader.ZTNativeReader",
    "SCREEN_ORIENTATION":"1",
    "startService": "true",
    "version": "0",
    "txtServiceStopped": "false",
    "msgCount": "0",
    "f_subcompany": "changan",
    "entities": [
       "t_parameter",
       "t_paramvalue",
       "t_notice",
       "t_check_paper_alarm",
       "t_check_paper",
       "t_check_plan_item",
       "t_check_plan",
       "t_check_paper_cooker",
       "t_check_paper_counter",
       "t_check_paper_defect",
       "t_check_paper_furnace",
       "t_check_paper_heater",
       "t_check_paper_innerplumb",
       "t_check_paper_meter",
       "t_phone_state",
       "t_check_paper_plumb",
       "t_check_paper_valve",
	   "t_blob",
	   "t_plan_item_meter",
	   "t_plan_item_device"
    ]
}