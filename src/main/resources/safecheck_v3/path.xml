<cfg>
	<!--app-->
	<!-- 获取安检计划 -->
	<path alias='getCheckPlan' path='getCheckPlan.path' mobile='true'/>
	<!-- 获取安检计划 -->
	<path alias='getNewCheckPlan' path='getNewCheckPlan.path' mobile='true'/>
	<!-- 获取工单信息 -->
	<path alias='getUserinfomation' path='getUserinfomation.path' mobile='true'/>
	<!-- 获取工单信息 -->
	<path alias='getPaperinfomation' path='getPaperinfomation.path' mobile='true'/>
	<!-- 获取安检计划 -->
	<path alias='safeServicePath' path='safeServicePath.path' mobile='true'/>
	<!-- 获取getCheckPlanItem计划 -->
	<path alias='getCheckPlanItem' path='getCheckPlanItem.path' mobile='true'/>
	<!-- 获取下发通知 -->
	<path alias='getNotice' path='getNotice.path' mobile='true'/>
	<!-- 获取安检单信息 -->
	<path alias='getCheckPaper' path='getCheckPaper.path' mobile='true'/>
	<!-- 获取user表信息 -->
	<path alias='getPlanItemMeter' path='getPlanItemMeter.path' mobile='true'/>
	<!-- 按小区获取user表信息 -->
	<path alias='getPlanMeterByAdress' path='getPlanMeterByAdress.path' mobile='true'/>
	<!-- 按预约时间获取user表信息 -->
	<path alias='getPlanItemByPrearrange' path='getPlanItemByPrearrange.path' mobile='true'/>
	<!-- 获取设备以及检查项 -->
	<path alias='getDevices' path='getDevices.path' mobile='true'/>
	<!-- 获取隐患 信息-->
	<path alias='getDefects' path='getDefects.path' mobile='true'/>
	<!-- 获取安检计划总数 -->
	<path alias='getPcCheckPaperCount' path='getPcCheckPaperCount.path' mobile='true'/>

	<path alias='getLocalCheckPlan' path='getLocalCheckPlan.path' mobile='true'/>
	<!-- 查询本地待办 -->
	<path alias='safecheckGetServiceMobile' path='safecheckGetServiceMobile.path' mobile='true'/>
	<!-- 查询本地已办 -->
	<path alias='getSafecheckCompletedInfo' path='getSafecheckCompletedInfo.path' mobile='true'/>
	<!-- 查询本地未传 -->
	<path alias='safecheckNotUpload' path='safecheckNotUpload.path' mobile='true'/>
	<!--获取计划项表信息-->
	<path alias='getItemMeter' path='getItemMeter.path' />
	<!--获取共享计划信息-->
	<path alias='getShareCheckPlan' path='getShareCheckPlan.path' />
	<path alias='getCheckPlantest' path='getCheckPlantest.path' />
	<path alias='getPlanItemSum' path='getPlanItemSum.path' />

	<!--pc-->
	<!-- 通过id获取paper -->
	<path alias='getCheckPaperById' path='getCheckPaperById.path'/>
	<!-- 按预约时间获取user表信息 -->
	<path alias='getItemByPrearrange' path='getItemByPrearrange.path' />
	<!-- 获取安检计划 -->
	<path alias='getPcCheckPaper' path='getPcCheckPaper.path' />
</cfg>
