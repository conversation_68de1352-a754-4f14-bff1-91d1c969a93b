//保存预约计划
log.info("开始保存预约计划{data}"),
existitem = 0, //添加计划已存在的个数
updateitem = 0, //添加计划的个数
data.planWay == "create" :(
    value= entity.partialSave("t_check_plan",
           {
                id:commonTools.getUUID(),//生成计划ID
                f_plan_name:data.f_plan_name,
                f_checker:data.f_checker,
                f_checker_id:data.f_checker_id,
                f_plan_year:convertTools.stringToInt(data.f_plan_year),
                f_plan_month:convertTools.stringToInt(data.f_plan_month),
                f_plan_type:data.f_plan_type,
                f_issued:data.f_issued,
                f_subcompany:data.f_subcompany,
                f_filialeid:data.f_filialeid,
                f_create_operator:data.f_create_operator,
                f_create_time: data.f_create_time,
                f_effective_start_time: data.f_effective_start_time,
                f_effective_end_time: data.f_effective_end_time,
                f_safecheck_type: data.f_safecheck_type
                }
    )
),

(
    value = {
        id : data.planid
    }
),

data.createWay != "create" : (

    length = 0,
    repeatedids = "",

       log.info("第三"),
    papam = {
       timetype:data.model.timetype,
       f_filialeids:data.model.f_filialeids,
       condition: data.model.condition
    },
    log.info("第四"),
    repeatedidArray = sql.query("getExistCheckItem", papam,-1),
    repeatedidArray.each (
        length = length + 1,
        length != data.repeated : (
            repeatedids = "{repeatedids}{row.f_userinfoid},"
        ), (
            repeatedids = "{repeatedids}{row.f_userinfoid}"
        )
    ),
    log.info("length ===> {length}")
), null,

data.createWay == "override" : (
    sql.execSQL("v3_safecheck_sql", "
        UPDATE t_check_plan_item SET f_state = '作废'
        WHERE f_state = '未检' AND f_userinfoid IN ({repeatedids})
    ")
), null,

log.info(value),
// 新增item数据
data.sqlname == "ByOrgChecker" :(

    sql.execSQL("v3_safecheck_sql", "
           INSERT INTO t_check_plan_item (
                    	id,
                    	f_userinfo_code,
                    	f_userinfoid,
                    	f_idnumber,
                    	f_area,
                    	f_slice_area,
                    	f_street,
                    	f_residential_area,
                    	f_building,
                    	f_unit,
                    	f_floor,
                    	f_room,
                    	f_address,
                    	f_state,
                    	f_upload_state,
                    	f_plan_id,
                    	f_user_name,
                    	f_user_phone,
                    	f_user_type,
                    	f_approved,
                    	f_no_checkplan,
                    	f_last_check_state,
                    	f_last_check_date,
                    	f_repair_approved,
                    	f_filialeid,
                    	version,
                    	f_last_check_result,
                        f_comments,
                        f_newfile_date,
                        f_safecheck_type
                    ) SELECT
                        id,
                        f_userinfo_code,
                        f_userinfo_id,
                        f_idnumber,
                        f_area,
                        f_slice_area,
                        f_street,
                        f_residential_area,
                        f_building,
                        f_unit,
                        f_floor,
                        f_room,
                        f_address,
                        '未检',
                        '未传',
                        '{value.id}',
                        f_user_name,
                        f_user_phone,
                        f_user_type,
                        '未审核',
                        '有计划安检',
                        f_last_check_state,
                        f_last_check_date,
                        '未审核',
                        '{data.f_filialeid}',
                        version,
                        f_last_check_result,
                        f_comments,
                        f_newfile_date,
                        '{data.f_safecheck_type}'
                      FROM (
                        SELECT
                        newId() id,
                        ui.f_userinfo_code,
                        uf.f_userinfo_id,
                        ui.f_idnumber,
                        ua.f_area,
                        ua.f_slice_area,
                        ua.f_street,
                        ua.f_residential_area,
                        ua.f_building,
                        ua.f_unit,
                        ua.f_floor,
                        ua.f_room,
                        ua.f_address,
                        ui.f_user_name,
                        ui.f_user_phone,
                        uf.f_user_type,
                        ui.f_last_check_state,
                        ISNULL(ISNULL(f_last_check_date,CONVERT(varchar(50),f_gas_date,23)),CONVERT(varchar(50),f_createfile_date,23)) f_last_check_date,
                        DATEDIFF({data.model.timetype}, ISNULL(ISNULL(f_last_check_date,f_gas_date),f_input_date),GETDATE()) f_time,
                        ui.version,
                        ui.f_last_check_result,
                        ui.f_comments,
                        ui.f_createfile_date f_newfile_date
                        FROM
                        t_userfiles uf
                        left join t_userinfo ui ON uf.f_userinfo_id = ui.f_userinfo_id
                        left join t_user_address ua on uf.f_userinfo_id = ua.f_userinfo_id
                        WHERE
                            uf.f_userinfo_id IS NOT NULL
                            AND ui.f_user_state  = '正常'
                            AND ui.f_filialeid in {data.model.f_filialeids}
                      ) a
                      WHERE {data.model.condition}
                      {data.createWay == "skip" : " AND f_userinfo_id NOT IN ({repeatedids}) ", ""}
    "),

    sql.execSQL("v3_safecheck_sql", "
        insert into t_plan_item_meter (id,f_weizhi,f_maker,f_diaphgram_size,f_aliasname,f_meter_type,f_prior_reading,f_meter_no,f_flow_direction,f_metertitles,f_metergasnums,f_plan_item_id,f_initial_base)
                 select
                 	newId(),
                 	f_position AS f_weizhi,
                 	gb.f_manufacturers AS f_maker,
                 	gm.f_meter_style AS f_diaphgram_size,
                 	gb.f_meter_brand AS f_aliasname,
                 	gm.f_type AS f_meter_type,
                 	f_meter_base AS f_prior_reading,
                 	f_meternumber AS f_meter_no,
                 	f_aroundmeter AS f_flow_direction,
                 	f_metertitles As f_metertitles,
                 	f_meter_base As  f_metergasnums,
                 	cpi.id   As  f_plan_item_id,
                 	uf.f_initial_base
                 FROM
                 	t_userfiles uf left join t_check_plan_item cpi on uf.f_userinfo_id = cpi.f_userinfoid  LEFT JOIN t_gasbrand gb ON uf.f_gasbrand_id = gb.id LEFT JOIN  t_gasmodel gm on uf.f_gasmodel_id = gm.id
                 WHERE uf.f_userinfo_id in(
                    SELECT
                        a.f_userinfo_id
                    FROM
                        (
                        SELECT
                            ui.f_userinfo_id,
                            ui.f_createfile_date,
                            f_userinfo_code,
                            ua.f_area,
                            ua.f_street,
                            ua.f_residential_area,
                            ua.f_building,
                            ua.f_unit,
                            ua.f_floor,
                            ua.f_room,
                            ua.f_address,
                            f_user_name,
                            f_user_phone,
                            uf.f_user_type,
                            f_last_check_state,
                            f_last_check_result,
                            ui.f_comments,
                            ui.version,
                            ui.f_filialeid,
                            ISNULL(ISNULL(f_last_check_date,CONVERT(varchar(50),f_gas_date,23)),CONVERT(varchar(50),f_createfile_date,23)) f_last_check_date,
                            DATEDIFF({data.model.timetype}, ISNULL(ISNULL(f_last_check_date,f_gas_date),f_input_date),GETDATE()) f_time
                        FROM
                            t_userfiles uf
                            left join t_userinfo ui ON uf.f_userinfo_id = ui.f_userinfo_id
                            LEFT JOIN t_user_address ua ON ui.f_userinfo_id = ua.f_userinfo_id
                        WHERE
                            uf.f_userinfo_id IS NOT NULL
                            AND ui.f_user_state  = '正常'
                            AND ui.f_filialeid in {data.model.f_filialeids}
                        ) a
                    WHERE {data.model.condition}
        	) and cpi.f_plan_id = '{value.id}'
            {data.createWay == "skip" : " AND uf.f_userinfo_id NOT IN ({repeatedids}) ", ""}

    ")
//    sql.query("UpdateUserInfoByOrgChecker",param),
//    sql.query("CreateCheckPlanByOrgChecker",param),
//    sql.query("CreatePlanMeterByOrgChecker",param)
),null,
countItem = 0,
data.planWay == "add" :(
    result = sql.querySQL("v3_safecheck_sql", "select count(*) count from t_check_plan_item where f_plan_id = '{data.planid}' and f_state != '作废'"),
    log.info("新增后该计划中共{result[0].count}条安检单"),
    countItem = result[0].count
),null,
sql.execSQL("v3_safecheck_sql", "
     update t_check_paper set f_danger_state = '1' where f_userinfo_code in
        (
                SELECT
                    f_userinfo_code
                FROM (
                    SELECT
                        ui.f_userinfo_id,
                        ui.f_createfile_date,
                        f_userinfo_code,
                        ua.f_area,
                        ua.f_street,
                        ua.f_residential_area,
                        ua.f_building,
                        ua.f_unit,
                        ua.f_floor,
                        ua.f_room,
                        ua.f_address,
                        f_user_name,
                        f_user_phone,
                        uf.f_user_type,
                        ua.f_last_check_state,
                        ua.f_last_check_result,
                        ui.f_comments,
                        ui.version,
                        ui.f_filialeid,
                        ISNULL(ISNULL(ua.f_last_check_date,CONVERT(varchar(50),f_gas_date,23)),CONVERT(varchar(50),f_createfile_date,23)) f_last_check_date,
                        DATEDIFF({data.model.timetype}, ISNULL(ISNULL(ua.f_last_check_date,f_gas_date),f_input_date),GETDATE()) f_time
                    FROM
                        t_userfiles uf
                        left join t_userinfo ui ON uf.f_userinfo_id = ui.f_userinfo_id
                        LEFT JOIN t_user_address ua ON ui.f_userinfo_id = ua.f_userinfo_id
                    WHERE
                        uf.f_userinfo_id IS NOT NULL
                        AND ui.f_user_state  = '正常'
                        AND ui.f_filialeid in {data.model.f_filialeids}
                ) a
                WHERE
                    {data.model.condition}
            )
      {data.createWay == "skip" : " AND f_userinfoid NOT IN ({repeatedids}) ", ""}
"),
{code:200,countItem:countItem}
