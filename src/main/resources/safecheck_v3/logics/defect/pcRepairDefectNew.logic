log.info("进入pcRepairDefectNew.logic ===> {data}"),
repairdate=dateTools.getNow2(),
f_defect_remark="",
data.has("f_defect_remark"):(
    f_defect_remark=data.f_defect_remark
),null,
// 更新t_devices_items
sql.execSQL("v3_safecheck_sql", "
	UPDATE t_devices_items SET f_is_repaired = '已处理',f_repair_path = '{data.filename}',f_repairman = '{data.f_repairman}',f_repair_date = '{data.f_repair_date}',f_audit_results = null,
	f_defect_remark='{f_defect_remark}'
	WHERE id = '{data.id}'
"),
//  根据隐患id查询checkpaperid
paperId = sql.querySQL("v3_safecheck_sql", "
    select cp.id paper_id from t_check_paper cp left join
    	t_paper_devices pd on cp.id = pd.f_paper_id
    	left join t_devices_items di on pd.id = di.f_device_id
    	where di.id = '{data.id}'
"),
log.info("查询到该隐患所在的安检单id为 >>> {paperId}"),
//  根据安检单查询是否还有未处理的隐患
papers=sql.querySQL("v3_safecheck_sql", "
    select di.f_item_name,cp.id
    from t_paper_devices pd
    left join t_devices_items di on pd.id=di.f_device_id
    left join t_devices_items_lists dil on dil.f_item_id=di.id
    left join t_check_paper cp on cp.id = pd.f_paper_id
    where cp.id='{paperId[0].paper_id}'
    and di.f_is_defect = 'true' and (di.f_deal_dispose in ('自行整改', '自行处理', '报修') and (di.f_is_repaired != '已处理' or di.f_is_repaired is null))
"),
log.info("该安检单目前的隐患为 >>> {papers}"),
papers.length() == 0 :(
    sql.execSQL("v3_safecheck_sql", "
        update t_check_paper set f_repaired='已修', f_repairman='{data.f_repairman}', f_repair_date = '{repairdate}' where id = '{paperId[0].paper_id}'
    "),
    //  解除限购
    singlevalue = sql.querySQL("v3_safecheck_sql", "SELECT value FROM t_singlevalue WHERE name IN ('工单完成时是否解除限购')"),
    singlevalue != [] :(
        singlevalue[0].value == "是" :(
            result = sql.querySQL("v3_safecheck_sql", "
                SELECT
                    f_userinfo_code
                FROM
                    t_check_paper
                WHERE
                    id = '{paperId[0].paper_id}'
            "),
            logic.run("CancelLimited",{f_userinfo_code: result[0].f_userinfo_code})
        ),null
    ),null
),null,
{code:200}
