log.info("进入pcRepairDefect.logic ===> {data}"),
// 上传图片
imgPath = FileSaveTools.saveFileReport("D:/安检",data.imgSrc,data.filename),
imgName = FileSaveTools.getPathFileName(imgPath),
imgType = subString.Split(imgName,"\\."),
sql.execSQL("v3_safecheck_sql", "
	INSERT INTO t_files(f_filename,f_filetype,f_downloadpath,f_realpath,f_uploaddate) VALUES('{imgName}','{imgType.s1}','{imgPath}','{imgPath}',GETDATE())"
),
// 更新t_devices_items
ENV.tenantName == "aode" :(
    data.f_item_remakes == null :(
    	data.f_item_remakes = ""
    ),null,
    sql.execSQL("v3_safecheck_sql", "
    	UPDATE t_devices_items SET f_is_repaired = '已处理',f_item_remakes = '{data.f_item_remakes}',f_repair_path = '{imgName}',f_repairman = '{data.f_repairman}',f_repair_date = '{data.f_repair_date}'
    	WHERE id = '{data.id}'"
    )
),(
    sql.execSQL("v3_safecheck_sql", "
        UPDATE t_devices_items SET f_is_repaired = '已处理',f_repair_path = '{imgName}',f_repairman = '{data.f_repairman}',f_repair_date = '{data.f_repair_date}'
        WHERE id = '{data.id}'"
    )
),
{code:200}
