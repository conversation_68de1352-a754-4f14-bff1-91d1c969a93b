log.info("安检单详情--->{data}"),
restul= sql.querySQL("v3_safecheck_sql", "select * from t_check_paper where  {data.condition}"),
restul[0].f_overall_imgs =sql.querySQL("v3_safecheck_sql", "select * from t_paper_overall_imgs where f_paper_id = '{restul[0].id}'"),
f_devices = sql.querySQL("v3_safecheck_sql", "select * from t_paper_devices where f_paper_id = '{restul[0].id}'"),
f_devices.each(
  row.f_items = sql.querySQL("v3_safecheck_sql", "select * from t_devices_items where f_device_id = '{row.id}'"),
  row.f_items.each(
    row.f_item_lists = sql.querySQL("v3_safecheck_sql", "select * from t_devices_items_lists where f_item_id = '{row.id}'")
  )
),
restul[0].f_devices = f_devices,
restul
