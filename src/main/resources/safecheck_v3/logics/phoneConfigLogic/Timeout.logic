log.debug(msg:$------定时提取开始-----$),
log.debug(msg:$定时提取:{context.f_repairman_id}$),
log.debug(msg:$定时提取:{context.cc_base_url}$),
log.debug(msg:$context.version:{context}$),
res = logic.run(alias:$SafeCheckTimeOut$,json:context),
re =  logic.run(alias:$TelTimeOut$,json:context),
r =  logic.run(alias:$ApplyTimeOut$,json:context),
res= res + re + r,log.debug(msg:$result:{res}$),
result = {},
res > 0:(
	result = {code:200,hasNewMsg:1}
),null,
result
