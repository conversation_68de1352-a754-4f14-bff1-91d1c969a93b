log.info("选择删除重复安检计划开始"),

f_userinfo_id="",
   log.info("开始循环"),
//循环选择计划
data.checked.each(
   log.info("循环数据===={row}"),
   sqlcondition={
   condition:"{data.condition} and i.f_userinfo_code ='{row.f_userinfo_code}'",
   f_plan_year:data.f_plan_year,
   f_plan_month:data.f_plan_month,
   f_filialeid:data.f_filialeid
   },
   log.info("sql===={sqlcondition}"),
   arrs = sql.query("根据年月安检员查询安检项",sqlcondition),
   log.info("重复数===={arrs}"),

   arrs.length()>1:(
   //有重复
       //删除计划项
        sql.execSQL("v3_safecheck_sql", "delete from t_plan_item_meter where f_plan_item_id = '{row.id}'"),
        sql.execSQL("v3_safecheck_sql", "delete from t_check_plan_item where id = '{row.id}'"),
        //更新用户安检时间
         user={
                 id:row.f_userinfo_code
               },
         logic.run("UpdateIssuedTime",user),

        //判断是否删除计划
       count=sql.querySQL("v3_safecheck_sql", "SELECT count(*) num from t_check_plan_item where f_plan_id = '{row.f_plan_id}'"),
       count[0].num==0:(
       log.info("计划无item,删除计划"),
       sql.execSQL("v3_safecheck_sql", "delete from t_check_plan where id = '{row.f_plan_id}'")
       ),(
         log.info("计划存在item,不删除计划")
       )
   ),(
   //无重复
    f_userinfo_id="{f_userinfo_id}   {row.f_userinfo_code}",
    log.info("包含重复id=={f_userinfo_id}")
   )
),
f_userinfo_id=="":(
    ret={
         code:200,
         mgs:"去重成功"
    }
),(
    ret={
        code:201,
        mgs:"包含不可去重计划",
        f_userinfo_id:f_userinfo_id
    }
)

