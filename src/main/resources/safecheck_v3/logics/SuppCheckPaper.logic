log.info("----------data:{data}-----------"),

data.paper.whetherrepair == 1 : (
    //发送维修消息
    log.info("-----发送维修消息{data}"),
    KafKadata =[],
    KafKadata.put(data),
    kafka.produceMsg("repair",KafKadata)
),null,
log.info("----------data:{data.paper}-----------"),
f_paper_id = entity.partialSave("t_check_paper", data.paper),
log.info("上传数据paper的f_paper_id----------------------》》{f_paper_id}"),
debugger,
data.paper.id!=null:(
sql.execSQL("v3_safecheck_sql", "
   update t_check_plan_item set f_state='已检',f_upload_state='已传',f_complete = '已完成',f_last_check_state='{data.paper.f_entry_status}',f_last_check_date='{data.paper.f_offsite_time}',
   version =version+1 where id = '{data.paper.f_check_item_id}'
")
),(
sql.execSQL("v3_safecheck_sql", "
   update t_check_plan_item set f_supplement_tag='补录',f_state='已检',f_upload_state='已传',f_complete = '已完成',f_last_check_state='{data.paper.f_entry_status}',f_last_check_date='{data.paper.f_offsite_time}',
   version =version+1 where id = '{data.paper.f_check_item_id}'
")
)
