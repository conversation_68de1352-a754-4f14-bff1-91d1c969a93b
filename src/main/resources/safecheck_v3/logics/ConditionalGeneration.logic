//根据以下四个参数生成sql条件
//data:{
 //   condition:'sql条件', 查询条件
 //     switchCheckAll:true/false是否有选择功能
  //  checkAll:true/false, 是否选中 ‘全选按钮’
   // param[...], 选中的字段的值
   // columnName 需要做查询的字段名
//}
log.info("---------data:{data}"),
strResult=data.condition,
strResult == "":(strResult = "1 = 1"),strResult,
log.info("当前data--{data}"),
data.switchCheckAll:(
    data.param.length()>0:(
            d=",",
            s=jsoncompare.jsonArrayFormat(data.param,d),
            data.checkAll:(
                strResult = "{strResult} and {data.columnName} not in ({s}) "
            ),
            (
                strResult = "{strResult} and {data.columnName} in ({s}) "
            )
    ),(
          data.checkAll:(
              strResult = "{strResult} "
          ),
          (
              strResult = "{strResult} and 1<1 "
          )
    )

),null,

log.info(strResult),
strResult
