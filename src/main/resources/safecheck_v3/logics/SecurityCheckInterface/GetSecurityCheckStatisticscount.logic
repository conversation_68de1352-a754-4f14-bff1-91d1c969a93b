//获取安检状态统计数据  入户数 和到访不遇的数量 完成
log.error("进入第三方查询安检业务获取安检状态统计数据"),
log.error("传入必填条件值为:{data}"),
try {
    result= {code:200,data:null,msg:"请求成功"},
    data.has("start_date") !=true: (
                result= {code:500,data:null,msg:"参数错误，请检查请求参数！"}
            ),(
                data.has("end_date") !=true: (
                result= {code:500,data:null,msg:"参数错误，请检查请求参数！"}
                ),(
                    // 判断用户类型
                    data.has("user_type") != true: (
                        data.f_user_type = ""
                    ),(
                        data.f_user_type = data.user_type
                    ),
                    // 判断计划名
                    data.has("plan_name") != true: (
                        data.f_plan_name = ""
                    ),(
                        data.f_plan_name = data.plan_name
                    ),
                     // 判断分公司编号
                     data.has("f_filialeid")&&data.f_filialeid!="": (
                          fids = "tcp.f_filialeid in (",
                             data.f_filialeid.length()-1 == data.f_filialeid.lastIndexOf(",")  :(
                                 // 如果最后一个值是逗号，去掉最后一个逗号
                                 data.f_filialeid = data.f_filialeid.substring(0,data.f_filialeid.length()-1)
                             ),null,

                             convertTools.splitString(data.f_filialeid,",").each(
                                 fids = "{fids}'row',"
                             ),
                             fids.substring(fids.length()-1) == "," :(
                                 fids = "{fids.substring(0,fids.length()-1)})"
                             ),null,
                             data.f_filialeid = fids
                     ),(
                      data.f_filialeid = ""
                     ),
                    log.error("打印最终传递的参数为{data}"),
                    resdata = sql.query("GetSecurityCheckStatisticss",data),
                    result= {code:200,data:resdata,msg:"请求成功"},
                    log.error("请求返回,信息为:{result}")
                )
            ),
result
} catch (Exception e)  {
    log.info("请求异常,信息为:{e}"),
    {code:501,data:null,msg:"异常"}
}


