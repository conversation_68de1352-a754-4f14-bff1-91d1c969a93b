log.debug("v4隐患整改{data}"),
date_time=dateTools.getNow2(),
papers = sql.querySQL("v3_safecheck_sql","
    select
        tcp.*,
        tu.f_zuhu_phone,
        tu.f_user_phone f_user_phone_ui
    from t_check_paper tcp WITH ( nolock )
        left join t_userinfo tu on tu.f_userinfo_id = tcp.f_userinfoid
    where
        tcp.id='{data.paper.cp_id}'
"),
repairDep=null,
repairMan=null,
data.has("f_meetunit"):(
    repairDep = sql.querySQL("v3_safecheck_sql","SELECT * FROM t_department WHERE id = '{data.f_meetunit}'")
),null,
data.has("f_repairman"):(
    repairMan = sql.querySQL("v3_safecheck_sql","SELECT * FROM t_user WHERE id = '{data.f_repairman}'")
),null,
loginDep = sql.querySQL("v3_safecheck_sql","SELECT * FROM t_department WHERE id = '{data.loginUser.orgid}'"),
loginDep.length() > 0: null,( loginDep = null),

data.dispatchType == "派发给维修员" : (
    toRepair = "一级派单",
    f_handlingtype = "派维修员"
),(
    toRepair = "",
    f_handlingtype = "转站点"
),
model={
    f_userinfo_code: papers[0].f_userinfo_code,
    f_userinfo_id: papers[0].f_userinfoid,
    f_user_name:papers[0].f_user_name,
    f_address: papers[0].f_address,
    f_user_type: papers[0].f_user_type,
    f_residential_area: papers[0].f_residential_area,
    f_contact_phone: papers[0].f_contact_phone,
    failure: "",
    f_source: "隐患复检",
    f_handlingtype: data.dispatchType,
    toRepair: "一级派单",
    f_meetunit: repairDep == null:"",repairDep[0].name,
    f_reciever: repairMan == null:"",repairMan[0].name,
    f_orgid : data.loginUser.orgid,
    f_orgname:loginDep == null:"",loginDep[0].name,
    f_operator: data.loginUser.name,
    f_operatorid: data.loginUser.ename,
    f_repairtype: "",
    f_remarks: data.f_remarks,
    f_repair_date: "",
    name: data.loginUser.name,
    ename: data.loginUser.ename,
    model : {
        f_user_type : papers[0].f_user_type
    }
},
result = jsonTools.convertToJson(restTools.post("/rs/logic/CreateWorkOrderOutside",model)),
result.has("f_service_id"):(
    sql.execSQL("v3_safecheck_sql","update t_check_paper set f_SafeToRepair_id = '{result.f_service_id}' where id = '{data.paper.cp_id}'")
),
result

