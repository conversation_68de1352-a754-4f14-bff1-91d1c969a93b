log.info("----8888888888888"),
log.info("--------------{data}-------"),
f_check_book_id = entity.partialSave("t_check_book", data.checkBook),
log.info("{f_check_book_id}"),
bookid = jsonTools.convertToJson(f_check_book_id).id,
sql.execSQL("v3_safecheck_sql", "update t_user_address set f_check_book_id = {bookid}
        FROM
            t_userinfo tui
                LEFT JOIN t_userfiles tuf ON tui.f_userinfo_id= tuf.f_userinfo_id
                LEFT JOIN t_user_address tua ON tuf.f_userinfo_id = tua.f_userinfo_id
                LEFT JOIN t_check_book tcb ON tua.f_check_book_id= tcb.id
        WHERE
                tui.f_user_state = '正常'
          AND ( tuf.f_table_state= '正常' OR tuf.f_table_state= '停用' )
          AND { data.sqlStr }"),
f_check_book_id
