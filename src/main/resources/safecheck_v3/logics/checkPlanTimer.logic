log.info("执行定时器，扫描计划"),
sql.execSQL("v3_safecheck_sql", "
    UPDATE t_user_address
    SET f_plan_id = NULL
    WHERE
        id IN (
            SELECT
                id
            FROM
                t_user_address
            WHERE
                f_plan_id IN (
                    SELECT
                        id
                    FROM
                        t_check_plan
                    WHERE
                        (f_end_time < ( SELECT GETDATE () ) or (f_plan_year = year(getdate()) and f_plan_month < datepart(mm,getdate())))
                )
        )
")
