log.info("隐患整改：{data}"),
log.info("进入updateDefectStateZG.logic ===> {data}"),
data.f_create_time=dateTools.getNow2(),
ENV.tenantName == "aode" :(
    repairedValue = "已转维修"
),(
    repairedValue = "转维修"
),
sql.execSQL("v3_safecheck_sql", "
	UPDATE t_devices_items SET f_is_repaired = '{repairedValue}'
	WHERE id IN {data.itemsId}"
),
ENV.tenantName == "aode" :(
    sql.execSQL("v3_safecheck_sql", "
    	UPDATE t_paper_hiddens SET f_is_repaired = '已转维修'
    	WHERE f_paper_id = '{data.paperid}'"
    )
),null,
//f_rectification_date='{data.f_create_time}'
sql.execSQL("v3_safecheck_sql", "
	UPDATE t_check_paper
	    SET
	        f_SafeToRepair_id='{data.serviceid}',
	        f_repaired='已转维修'
    where id='{data.paperid}'"
),
ENV.tenantName == "aode" :(
    checkToRepair = {
        f_check_paper_id: data.paperid,
        f_service_id: data.serviceid,
        f_create_date: dateTools.getNow2()
    },
    // 保存安检转维修中间表记录
    entity.partialSave("t_check_turn_repair", checkToRepair)
),null,
{code:200}
