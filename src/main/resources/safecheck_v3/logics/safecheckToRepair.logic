log.info("安检转维修======{data}"),

restJson = {action: "post", url: "{context.cc_base_url}/api/af-safecheck/logic/callerSend", data:data.data.toString()},

res = restTools.action(restJson),
log.info("安检转维修结果:{res}"),

params = {
    f_paper_id: data.f_paper_id,
    f_SafeToRepair_id:res.data.f_service_id
},
retJson = {action: "post", url: "{context.cc_base_url}/api/af-safecheck/logic/updatePaperRepair", data:params.toString()},

ret = restTools.action(retJson),

ret
