data.rows != null :(
	meters = data.rows
),
data.remove("rows"),

users=sql.query("FindUserById",data),
users.length() >= 1 : (
	throw {status: 505, msg : "用户编号重复"}
),
users.length() < 1 :(
		userid = entity.partialSave("t_userfiles",data)
),null,

//sql.execSQL("v3_safecheck_sql", "delete from t_meterfiles where f_userinfoid = '{data.f_userinfoid}'"),

	meters.each(
	meternum = sql.query("getmeternum", {f_userfile_id : userid, f_meter_no : row.f_meter_no}),
	meternum.length() <1 : (
	//表号不重复 直接保存
		meterid = entity.partialSave("t_meterfiles",row),
		sql.execSQL("v3_safecheck_sql", "
			update t_meterfiles set f_userfile_id ='{userid}' where id ='{meterid}'
		")
	),
	meternum.length() >=1:(
	//表号重复
		throw {status: 502, msg: "表号重复"}
	),null
)
