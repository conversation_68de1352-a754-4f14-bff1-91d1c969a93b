log.info("---------data:{data}"),
sqlstr = data.condition,
data.f_create_time=dateTools.getNow2(),
log.info("当前时间{data.f_create_time}"),
sql.execSQL("v3_safecheck_sql", "
        INSERT INTO t_check_plan_item (
                            id,
                            f_userinfo_code,
                            f_userinfoid,
                            f_idnumber,
                            f_slice_area,
                            f_street,
                            f_residential_area,
                            f_building,
                            f_unit,
                            f_floor,
                            f_room,
                            f_address,
                            f_state,
                            f_upload_state,
                            f_plan_id,
                            f_user_name,
                            f_user_phone,
                            f_user_type,
                            f_approved,
                            f_no_checkplan,
                            f_last_check_state,
                            f_last_check_date,
                            f_repair_approved,
                            f_filialeid,
                            f_create_time,
                            version,
                            f_last_check_result,
                            f_comments,
                            f_addressid,
                            f_safecheck_type,
                            f_subscribe_date,
                            f_last_check_date2,
                            f_last_check_state2,
                            f_remark,
                            f_open_date,
                            f_enter_number,
                            f_zuhu_phone,
                            f_rent_phone,
                            f_rent_name,
                            f_newfile_date
                            ) SELECT
                             {ENV.dbType == "sqlserver":"newId()", "SUBSTR(sys_guid(),0)"},
                            ui.f_userinfo_code,
                            ui.f_userinfo_id,
                            ui.f_idnumber,
                            ua.f_slice_area,
                            ua.f_street,
                            ua.f_residential_area,
                            ua.f_building,
                            ua.f_unit,
                            ua.f_floor,
                            ua.f_room,
                            ua.f_address,
                            '未检',
                            '未传',
                            '{data.f_plan_id}',
                            ui.f_user_name,
                            ui.f_user_phone,
                            uf.f_user_type,
                            '未审核',
                            '有计划安检',
                            '',
                            '',
                            '未审核',
                            ui.f_filialeid,
                            {ENV.dbType == "sqlserver":"'{data.f_create_time}'", "to_char('{data.f_create_time}','yyyy-MM-dd hh24:mi:ss')"},
                            ui.version,
                            '',
                            ui.f_comments,
                            ua.id,
                            '{data.f_safecheck_type}',
                            '{data.f_subscribe_date}',
                            '',
                            '',
                            ui.f_check_remarks,
                            ui.f_open_date f_open_date,
                            ua.f_enter_number,
                            ui.f_zuhu_phone,
                            ui.f_rent_phone,
                            ui.f_rent_name,
                            ui.f_createfile_date f_newfile_date
                            FROM
                               (
                                       select
                                           *,
                                           case PATINDEX('%[^0-9]%', f_building) when 0 then f_building else 999 end building,
                                           case PATINDEX('%[^0-9]%', f_unit) when 0 then f_unit else 999 end unit,
                                           case PATINDEX('%[^0-9]%', f_floor) when 0 then f_floor else 999 end floor,
                                           case PATINDEX('%[^0-9]%', f_room) when 0 then f_room else 999 end room
                                       from t_user_address
                                   ) ua
                               join t_userinfo ui ON ui.f_userinfo_id = ua.f_userinfo_id
                               left join t_area area on ua.F_RESIDENTIAL_AREA_ID=area.id
                               left join t_userfiles uf on uf.f_userinfo_id = ui.f_userinfo_id
                               left join t_meter_book mb on mb.id=uf.f_meter_book_num
                               left join t_userproperties up on ui.f_userinfo_id=up.f_userinfo_id and up.f_state='有效' and up.f_parameter_key='blackList'
                            WHERE {sqlstr}
                                  and uf.f_table_state in ('正常','停用','待开通') and ui.f_user_state!='销户'
"),
log.info("结果---{arrs}"),
                 //生成对应meter表信息
                 sql.execSQL("v3_safecheck_sql", "insert into t_plan_item_meter
                    (id,f_weizhi,f_maker,f_diaphgram_size,f_aliasname,
                    f_meter_type,f_prior_reading,f_meter_no,f_flow_direction,
                    f_metertitles,f_metergasnums,f_collection_type,f_total_gas,f_total_fee,f_meter_base,
                    f_plan_item_id)
                          select
                            {ENV.dbType == "sqlserver":"newId()", "SUBSTR(sys_guid(),0)"},
                            uf.f_position AS f_weizhi,
                            gb.f_manufacturers AS f_maker,
                            gm.f_meter_style AS f_diaphgram_size,
                            gb.f_meter_brand AS f_aliasname,
                            gb.f_meter_type,
                            f_meter_base AS f_prior_reading,
                            uf.f_meternumber AS f_meter_no,
                            uf.f_aroundmeter AS f_flow_direction,
                            uf.f_metertitles As f_metertitles,
                            uf.f_meter_base As  f_metergasnums,
                            gb.f_collection_type,
                            uf.f_total_gas,
                            uf.f_total_fee,
                            uf.f_meter_base,
                             cpi.id As  f_plan_item_id
                          FROM
                             (
                               select
                                   *,
                                   case PATINDEX('%[^0-9]%', f_building) when 0 then f_building else 999 end building,
                                   case PATINDEX('%[^0-9]%', f_unit) when 0 then f_unit else 999 end unit,
                                   case PATINDEX('%[^0-9]%', f_floor) when 0 then f_floor else 999 end floor,
                                   case PATINDEX('%[^0-9]%', f_room) when 0 then f_room else 999 end room
                               from t_user_address
                            ) ua
                            join t_userinfo ui ON ui.f_userinfo_id = ua.f_userinfo_id
                            left join t_area area on ua.F_RESIDENTIAL_AREA_ID=area.id
                            left join t_userfiles uf on ua.f_userinfo_id=uf.f_userinfo_id
                            left join t_meter_book mb on mb.id=uf.f_meter_book_num
                            left join t_userproperties up on ui.f_userinfo_id=up.f_userinfo_id and up.f_state='有效' and up.f_parameter_key='blackList'
                            left join (select * from (select f_userinfoid,id,row_number() over(partition by f_userinfoid order by f_create_time desc) r from t_check_plan_item) t where r=1) cpi on uf.f_userinfo_id = cpi.f_userinfoid
                            LEFT JOIN t_gasbrand gb ON uf.f_gasbrand_id = gb.id
                            LEFT JOIN  t_gasmodel gm on uf.f_gasmodel_id = gm.id
                          WHERE
                            {sqlstr}
                            and uf.f_table_state in ('正常','停用','待开通') and ui.f_user_state!='销户'
                 "),

// 添加派单动作，注意继承关系
//最后修改地址中的安检计划id
sql.execSQL("v3_safecheck_sql", "update t_user_address set f_check_version=null,f_plan_id='{data.f_plan_id}'
    where  id in ( select ua.id from (
        select
             *,
             case PATINDEX('%[^0-9]%', f_building) when 0 then f_building else 999 end building,
             case PATINDEX('%[^0-9]%', f_unit) when 0 then f_unit else 999 end unit,
             case PATINDEX('%[^0-9]%', f_floor) when 0 then f_floor else 999 end floor,
             case PATINDEX('%[^0-9]%', f_room) when 0 then f_room else 999 end room
        from t_user_address
    ) ua
    join t_userinfo ui ON ui.f_userinfo_id = ua.f_userinfo_id
    left join t_area area on ua.F_RESIDENTIAL_AREA_ID=area.id
    left join t_userfiles uf on uf.f_userinfo_id = ui.f_userinfo_id
    left join t_meter_book mb on mb.id=uf.f_meter_book_num
    left join t_userproperties up on ui.f_userinfo_id=up.f_userinfo_id and up.f_state='有效' and up.f_parameter_key='blackList'
    where {sqlstr}  and uf.f_table_state in ('正常','停用','待开通') and ui.f_user_state!='销户')"),
{code:200,result:arrs}



