// 将用户划分区域
log.info("参数:{data}"),
ids = "{data.ids}",
id = ids.replace("[","("),
id1 = id.replace("]",")"),
id2 = "",
log.info("id------>{id1}"),
pianqu = "'{data.pianqu}'",
checkall = data.checkedAll,
log.info("-------------------------------"),
log.info(checkall),
checkall:(
        log.info("sql参数------>{data.condition}"),
        items = sql.querySQL("v3_safecheck_sql", "
        SELECT
        	u.f_userinfo_id
        FROM
        	t_userinfo u
        	LEFT JOIN t_user_address a ON a.f_userinfo_id = u.f_userinfo_id
        WHERE {data.condition}
        "),
        log.info(" items------>{items}"),
        log.info(" 条数------>{items.length()}"),
        items.each(
            id2 = "{id2},{row.f_userinfo_id}"
        ),

        id3 = id2.replaceFirst(",","("),
        id4 = ")",
        id5 = "{id3}{id4}",
        log.info(id5),
        //log.info(id2),
        sql.execSQL("v3_safecheck_sql", "
                    update t_userinfo set f_check_area = {pianqu} where  f_userinfo_id in {id5}
                "),
        {code:200}
),(
    sql.execSQL("v3_safecheck_sql", "
            update t_userinfo set f_check_area = {pianqu} where  f_userinfo_id in {id1}
        "),
        {code:200}
),null




