log.debug("------安检单 抽检-----"),
log.debug("传入条件值为:{data}"),
try {
sql.querySQL("v3_safecheck_sql","
    update t_check_paper set
    f_spot_check_name = '{data.userName}',
    f_spot_check_state = '{data.spotCheckState}',
    f_spot_check_result = '{data.spotCheckResult}',
    f_spot_check_comments = '{data.spotCheckMessage}',
    f_spot_check_time = '{date.getNow2()}'
    where id = '{data.paperId}'
"),
200
} catch (Exception e)  {
501
}
