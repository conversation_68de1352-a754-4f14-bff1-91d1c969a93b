log.info("--------------执行回写{data}"),
size=sql.querySQL("v3_safecheck_sql", "select f_userinfoid from t_check_paper where f_supplement_tag is null and f_entry_status='入户'"),
size.length()>0:(
list=sql.querySQL("v3_safecheck_sql", "
SELECT
    tcp.id,
	tcp.f_userinfoid,
	tdi.f_item_name,
	tdil.f_item_value
FROM
	t_check_paper tcp
	LEFT JOIN t_paper_devices tpd ON tcp.id= tpd.f_paper_id
	LEFT JOIN t_devices_items tdi on tpd.id=tdi.f_device_id
	LEFT JOIN t_devices_items_lists tdil on tdi.id=tdil.f_item_id
where
f_device_type='{data.devicetype}'
and f_item_name ='{data.itemname}'
and f_entry_status='入户'
and f_supplement_tag is null
"),
log.info("回写数量{list.length()}"),
log.info("回写数据{list}"),
list.each(
row.f_item_value!=null&&row.f_item_value!="":(
sql.execSQL("v3_safecheck_sql", "update {data.table} set {data.changekey}='{row.f_item_value}' where f_userinfo_id='{row.f_userinfoid}'"),
log.info("更新的配置项为==>{row.f_item_name},表名为==>{data.table},
字段名为==>{data.changekey},userinfoid为==>{row.f_userinfoid},更新为==>{row.f_item_value},更新成功"),
sql.execSQL("v3_safecheck_sql", "update t_check_paper set f_supplement_tag ='是' where id='{row.id}'")
),(
log.info("值为null或者值为空不做更新")
)
)
),(
log.info("没有需要回写的安检单")
),
"200"
