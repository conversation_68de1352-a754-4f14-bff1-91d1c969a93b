log.info("ServiceTimeOut{data.id}"),
log.info("ServiceTimeOut{context.cc_base_url}"),


// 提取数据, body是参数，cc_base_url是提取路径

// 获得安检计划情况
checkplan = restTools.action({
	action: "post",
	url: "{context.cc_base_url}/api/af-safecheck/path/getCheckPlan",
	data:data.id
}),
log.info("ServiceTimeOut==>安检计划：{checkplan}"),

// 获得安检计划项情况
checkplanItem = restTools.action({
	action: "post",
	url: "{context.cc_base_url}/api/af-safecheck/path/getCheckPlanItem",
	data:data.id
}),
log.info("CheckoutPlan==>安检计划：{checkplanItem}"),
entity.partailsave("t_check_plan_item",checkplanItem),

{}
