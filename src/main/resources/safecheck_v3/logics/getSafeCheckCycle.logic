
res1 = sql.querySQL("v3_safecheck_sql", "select value from t_singlevalue where name='民用安检周期'"),
res2 = sql.querySQL("v3_safecheck_sql", "select value from t_singlevalue where name='非民用安检周期'"),

res1.length() == 0:(
    result = {
        msg:"未配置民用安检周期",
        code:500
    }
),(
    res2.length() == 0:(
        result = {
                msg:"未配置非民用安检周期",
                code:500
            }
    ),(
        result = {
            civil : res1[0],
            civilian : res2[0],
            code:200
        }
    )
),


result
