data.rows != null :(
	meters = data.rows
),
data.remove("rows"),
users=sql.query("FindUserById",data),
users.length() <1 : (
	throw {status: 505, msg : "该用户不存在"}
	),
users.length() == 1 : (
		json={},
		upt = jsoncompare.getUpdateSql("t_userfiles",data,json),
		sql.execSQL("v3_safecheck_sql", "
			{upt} where f_userinfoid = '{data.f_userinfoid}'
		")
	),null,

	sql.execSQL("v3_safecheck_sql", "delete from t_meterfiles where f_userinfoid = '{data.f_userinfoid}'"),

	meters.each(
	meternum = sql.query("getmeternum", {f_userfile_id : row.f_userfile_id, f_meter_no : row.f_meter_no}),
	meternum.length() <1 : (
	//表号不重复 直接保存
		entity.partialSave("t_meterfiles",row)
	),
	meternum.length() >=1:(
	//表号重复
		throw {status: 502, msg: "表号重复"}
	),null
)
