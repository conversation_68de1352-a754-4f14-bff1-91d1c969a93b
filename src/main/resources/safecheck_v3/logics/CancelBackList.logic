// 定时处理需要解除拉黑的用户
res=sql.querySQL("v3_safecheck_sql", "
    select * from t_check_paper
    where f_upload_date>DATEADD(day,-1,getdate()) and f_stop_service in ('待解除','待更新','待拉黑')
"),
log.info("查询到的用户res"),
res.each(
            row.f_stop_service=="待解除":(
              log.info("进入取消拉黑判断----------------------》》{row}"),
              black={
                  f_operat_type: "取消拉黑",
                  f_state: "有效",
                  f_user_name: "{row.f_user_name}",
                  f_comments: "该用户已入户安检结束拉黑",
                  f_operator: "{row.f_checker_name}",
                  f_operatorid: "{row.f_checker_id}",
                  f_orgid: "{row.f_filialeid}",
                  f_orgname: "",
                  f_depid: "",
                  f_depname: "",
                  f_operate_date: "{row.f_upload_date}",
                  f_describe: "{row.f_checker_name}对客户{row.f_user_name}进行取消拉黑"
              },
                black.f_userinfo_id = {
                     f_userinfo_id: row.f_userinfoid
                 },
              log.info("进入发送取消拉黑请求----------------------》》{black}"),
              params = jsonTools.convertToJson(restTools.post("/rs/logic/backlistOperate",black)),
              log.info("发送完成后返回-->>{params}"),
              sql.execSQL("v3_safecheck_sql", "update t_check_paper set f_stop_service='已解除' where id='{row.id}'")
            ),null,
            row.f_stop_service=="待更新":(
            log.info("进入更新拉黑原因----------------------》》{row}"),
                sql.execSQL("v3_safecheck_sql", "
                    update t_userproperties set f_comments='该用户入户安检时发现隐患！'
                    where f_userinfo_id='{row.f_userinfoid}' and f_state='有效'
                "),
                sql.execSQL("v3_safecheck_sql", "update t_check_paper set f_stop_service='已更新' where id='{row.id}'")
            ),null,
            row.f_stop_service=="待拉黑":(
              log.info("进入拉黑----------------------》》{row}"),
              black={
                  f_operat_type: "拉黑",
                  f_state: "有效",
                  f_user_name: "{row.f_user_name}",
                  f_comments: "该用户入户安检时发现隐患！",
                  f_operator: "{row.f_checker_name}",
                  f_operatorid: "{row.f_checker_id}",
                  f_orgid: "{row.f_filialeid}",
                  f_orgname: "",
                  f_depid: "",
                  f_depname: "",
                  f_operate_date: "{row.f_upload_date}",
                  f_describe: "{row.f_checker_name}对客户{row.f_user_name}进行拉黑",
                  safeCheck:1,
                  f_limit_times:1,
                  f_limit_value:30
              },
                black.f_userinfo_id = {
                     f_userinfo_id: row.f_userinfoid
                 },
              log.info("进入发送拉黑请求----------------------》》{black}"),
              params = jsonTools.convertToJson(restTools.post("/rs/logic/backlistOperate",black)),
              log.info("发送完成后返回-->>{params}"),
              sql.execSQL("v3_safecheck_sql", "update t_check_paper set f_stop_service='已拉黑' where id='{row.id}'")
            ),null

)

