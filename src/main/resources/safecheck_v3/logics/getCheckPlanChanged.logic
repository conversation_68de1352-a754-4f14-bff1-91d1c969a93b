// 获取工单变化情况
// 输入数据：{f_checker: '安检员名字', services: [{id: 安卓端工单编号, id_back: pc端工单编号}]}
// 输出数据：{inserts: [{含故障信息的工单}, deletes: [{id_back: 工单编号}], modified: [要修改的工单,含id,id_back,新service_version]]
log.info("getServiceChanged==>{data}"),

pageSize = 5000,

pcServicesCount = sql.querySQL("v3_safecheck_sql", "
    select cp.id,count(it.id) sum0 from t_check_plan cp
    LEFT JOIN t_check_plan_item it on it.F_PLAN_ID=cp.id
    where  cp.f_issued='是' and cp.f_checker_id = '{data.f_checker}'
    and (
    cp.f_plan_year = year(getdate()) or cp.f_safecheck_type ='送气类型'
    )
    group by cp.id
    having count(it.id)>0
"),
log.info("PC安检计划总数查询结果{pcServicesCount}"),

pcServices = [],
pcServicesCount.each(
    log.info("PC安检计划总数{ row.sum0}"),
     row.sum0 <= pageSize :(
    log.info("只够分一页"),
    pageCount = 1
    ),(
    pageCount = row.sum0/pageSize,
    size =  row.sum0 % pageSize,
    log.info("分页有没有余数{size}"),
    size == 0 :(
        pageCount = pageCount
    ),(
        pageCount = pageCount + 1
    ),null
    ),
    log.info("分页页码总数{pageCount}"),
    countnum = jsonTools.getArray(pageCount),
    num = 1,
    planarr = jsonTools.getArray(),
    planid = row.id,

    countnum.each(
      // 获取安检计划
      params = {
          data: {
              id: planid,
              pageCount:num,
              pageSize:pageSize
          }
      },
      log.info("调用方法参数{pageCount},{pageSize},{params}"),
      items =  logic.run("getPcCheckPaper",params),
      planarr = service.mergedArray(planarr,items),
      num = num + 1
    ),
    pc = sql.querySQL("getCheckPlanById","select * from t_check_plan where id = '{planid}'"),
    log.info("查询当前计划，{pc}"),
    pc[0].put("f_items",planarr),
    pcServices.put(pc[0])
),

// 返回工单变化情况
result = service.getChanged(data.services.rows, pcServices),

{result:result}
