log.info("安检审核---{data}"),
result={code:709,msg:"未识别操作！"},

data.operator=="success":(
    sql.execSQL("v3_safecheck_sql", "update t_check_paper set f_approved_by='{data.user_name}',f_approved_time='{data.audit_time}',f_approved='{data.audit_result}' where id='{data.id}'"),
    //更新计划项
    sql.execSQL("v3_safecheck_sql", "update t_check_plan_item set version = version + 1, f_approved = '{data.audit_result}', f_approvedstart_time = '{data.f_approvedstart_time}',f_approved_by='{data.user_name}', f_approved_time='{data.audit_time}' where id='{data.f_check_item_id}'"),
    result={code:200,msg:"安检审核成功！"}
),null,

data.operator=="fail":(
    sql.execSQL("v3_safecheck_sql", "update t_check_paper set f_approved_by='{data.user_name}',f_approved_time='{data.audit_time}',f_approved='{data.audit_result}',f_approved_note='{data.message}' where id='{data.id}'"),
    //更新计划项
    sql.execSQL("v3_safecheck_sql", "update t_check_plan_item set version = version + 1, f_approvedstart_time = '{data.f_approvedstart_time}',f_approved = '{data.audit_result}', f_state='已检',f_upload_state='未传',f_approved_by='{data.user_name}', f_approved_time='{data.audit_time}', f_approved_note='{data.message}' where id='{data.f_check_item_id}'"),
    result={code:200,msg:"安检审核成功！"}
),null,

result
