log.info("---------------{data}"),
// 饼状图隐患数量比例图
pieshuliang = sql.querySQL("v3_safecheck_sql", "
    SELECT
    	pd.f_device_type AS name,
    	COUNT ( di.id ) AS value
    FROM
    	t_check_paper cp
    	LEFT JOIN t_paper_devices pd on cp.id = pd.f_paper_id
    	LEFT JOIN t_devices_items di ON pd.id = di.f_device_id
    WHERE
    	di.f_is_defect = 'true' and {data.condition}
    GROUP BY
    	pd.f_device_type
"),
// 饼状图隐患等级
piedengji = sql.querySQL("v3_safecheck_sql", "
    SELECT
    	di.f_defect_level AS name,
    	COUNT ( di.id ) AS value,
    	'true' as selected
    FROM
    	t_check_paper cp
    	LEFT JOIN t_paper_devices pd on cp.id = pd.f_paper_id
    	LEFT JOIN t_devices_items di ON pd.id = di.f_device_id
    WHERE
    	di.f_is_defect = 'true' and {data.condition}
    GROUP BY
    	di.f_defect_level
"),
// 柱状图
bardata = sql.querySQL("v3_safecheck_sql", "
    select
        b1.* ,
        case when b2.xiaochu is null then 0 else b2.xiaochu end as xiaochu ,
        case when b2.xiaochu is null then b1.count else (b1.count - b2.xiaochu) end as shengyu
        from
    (
        SELECT
         top 10
        di.f_item_name AS name,
        COUNT ( di.id ) AS count
    FROM
        t_check_paper cp
        LEFT JOIN t_paper_devices pd on cp.id = pd.f_paper_id
        LEFT JOIN t_devices_items di ON pd.id = di.f_device_id
    WHERE
        di.f_is_defect = 'true' and {data.condition}
    GROUP BY
        di.f_item_name
    ORDER BY count desc
    ) b1
    left join
    (
            SELECT
                di.f_item_name AS name,
                COUNT ( di.id ) AS xiaochu
            FROM
                t_check_paper cp
                LEFT JOIN t_paper_devices pd on cp.id = pd.f_paper_id
                LEFT JOIN t_devices_items di ON pd.id = di.f_device_id
            WHERE
                di.f_is_defect = 'true'
                and di.f_is_repaired = '已处理'
                and di.f_item_name in (
                select t1.name from (
                    SELECT
                         top 10
                        di.f_item_name AS name,
                        COUNT ( di.id ) AS value
                    FROM
                        t_check_paper cp
                        LEFT JOIN t_paper_devices pd on cp.id = pd.f_paper_id
                        LEFT JOIN t_devices_items di ON pd.id = di.f_device_id
                    WHERE
                        di.f_is_defect = 'true' and {data.condition}
                    GROUP BY
                        di.f_item_name
                    ORDER BY value desc
                ) t1
                )
            GROUP BY
                di.f_item_name
    ) b2 on b1.name = b2.name
"),
result = {
    code: 200,
    pieshuliang: pieshuliang,
    piedengji: piedengji,
    bardata: bardata
},
result
