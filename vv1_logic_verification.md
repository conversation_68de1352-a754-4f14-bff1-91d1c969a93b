# vv1新增限购逻辑验证

## 修正后的业务逻辑

### 1. 查询逻辑修正
**修改前（错误）**：
```sql
SELECT * FROM t_limit_gas WHERE f_userinfo_id = ? AND f_State = '有效'
```

**修改后（正确）**：
```sql
SELECT * FROM t_limit_gas WHERE f_userinfo_id = ?
```

**说明**：现在查询该用户的所有限购记录，不管状态是什么。

### 2. 更新逻辑修正
**修改前**：
```sql
UPDATE t_limit_gas SET 
f_limit_style = ?, f_time_type = ?, f_limit_type = ?, 
f_start_date = ?, f_third_pay = ?, f_limit_times = ?, 
f_operate_date = ?, f_operate_people = ?, f_operate_peopleid = ?
WHERE id = ?
```

**修改后（正确）**：
```sql
UPDATE t_limit_gas SET 
f_limit_style = ?, f_time_type = ?, f_limit_type = ?, 
f_start_date = ?, f_third_pay = ?, f_limit_times = ?, 
f_operate_date = ?, f_operate_people = ?, f_operate_peopleid = ?, 
f_state = '有效'  -- ✅ 重要：确保状态设为有效
WHERE id = ?
```

### 3. 完整业务流程

#### 场景1：用户首次新增限购
1. 查询：`SELECT * FROM t_limit_gas WHERE f_userinfo_id = 93172`
2. 结果：无记录
3. 执行：INSERT新记录，状态为'有效'
4. 插入t_record记录，操作类型='新增限购'
5. 更新t_limit_gas.f_limit_recod_id

#### 场景2：用户已有限购记录（状态可能是'无效'）
1. 查询：`SELECT * FROM t_limit_gas WHERE f_userinfo_id = 93172`
2. 结果：找到记录（可能状态是'无效'）
3. 执行：UPDATE现有记录，**重要：把f_state设为'有效'**
4. 插入t_record记录，操作类型='修改限购'
5. 更新t_limit_gas.f_limit_recod_id

## 测试用例

### 测试用例1：新用户新增限购
```json
{
  "f_limit_style": "按次数",
  "f_time_type": "月度限购", 
  "f_limit_type": "气量",
  "f_start_date": "2024-01-15",
  "f_third_pay": "是",
  "f_limit_times": "10",
  "f_userinfo_id": "99999",  // 新用户ID
  "operator": "何文强",
  "operatorId": "339057283126394882"
}
```

**期望结果**：
- t_limit_gas表新增一条记录，f_state='有效'
- t_record表新增一条记录，f_operat_type='新增限购'

### 测试用例2：已有用户重新启用限购
```json
{
  "f_limit_style": "按金额",
  "f_time_type": "年度限购",
  "f_limit_type": "支付限制", 
  "f_start_date": "2024-02-01",
  "f_third_pay": "否",
  "f_limit_times": "5",
  "f_userinfo_id": "93172",  // 已存在的用户ID
  "operator": "何文强",
  "operatorId": "339057283126394882"
}
```

**期望结果**：
- t_limit_gas表更新现有记录，f_state='有效'，其他字段更新为新值
- t_record表新增一条记录，f_operat_type='修改限购'

## 验证SQL

### 验证新增/修改是否成功
```sql
-- 查看限购记录
SELECT 
    id, f_userinfo_id, f_limit_style, f_time_type, f_limit_type,
    f_start_date, f_third_pay, f_limit_times, f_state,
    f_limit_recod_id, f_operate_date, f_operate_people
FROM t_limit_gas 
WHERE f_userinfo_id = 93172;
```

### 验证操作记录
```sql
-- 查看操作记录
SELECT 
    id, f_operat_type, f_describe, f_state, f_operate_date,
    f_operator, f_user_name
FROM t_record 
WHERE f_operat_type IN ('新增限购', '修改限购')
AND f_user_id = '93172'
ORDER BY id DESC;
```

### 验证关联关系
```sql
-- 验证限购记录和操作记录的关联
SELECT 
    lg.id as limit_id,
    lg.f_state as limit_state,
    lg.f_limit_recod_id,
    r.id as record_id,
    r.f_operat_type,
    r.f_describe
FROM t_limit_gas lg
LEFT JOIN t_record r ON lg.f_limit_recod_id = r.id
WHERE lg.f_userinfo_id = 93172;
```
